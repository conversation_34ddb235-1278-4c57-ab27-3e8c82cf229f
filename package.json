{"name": "blp3.0", "version": "1.0.0", "private": true, "scripts": {"dev": "next dev", "dev:clean": "node scripts/clear-cache.js && timeout /t 2 /nobreak >nul && next dev", "dev:fresh": "node scripts/clear-cache.js && timeout /t 3 /nobreak >nul && set NEXT_CACHE_DISABLED=true && next dev", "dev:force": "node scripts/clear-cache.js && timeout /t 5 /nobreak >nul && next dev", "dev:nuclear": "powershell -ExecutionPolicy Bypass -File scripts/force-clear.ps1 && timeout /t 3 /nobreak >nul && next dev", "build": "next build", "build:clean": "node scripts/clear-cache.js && next build", "start": "next start", "lint": "next lint", "clear-cache": "node scripts/clear-cache.js", "nuclear-clear": "powershell -ExecutionPolicy Bypass -File scripts/force-clear.ps1", "kill-and-clean": "node scripts/clear-cache.js && timeout /t 3 /nobreak >nul", "reset": "node scripts/clear-cache.js && echo. && echo Wait 3 seconds then run 'npm run dev'"}, "dependencies": {"@genkit-ai/core": "^1.15.5", "@genkit-ai/firebase": "^1.15.5", "@genkit-ai/flow": "^0.5.17", "@genkit-ai/googleai": "^1.15.5", "@google/generative-ai": "^0.24.1", "@radix-ui/react-checkbox": "^1.1.3", "@radix-ui/react-dialog": "^1.1.2", "@radix-ui/react-label": "^2.1.1", "@radix-ui/react-select": "^2.0.0", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slot": "^1.1.0", "@react-pdf/renderer": "^4.1.6", "@shadcn/ui": "^0.0.4", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "firebase": "^12.0.0", "lucide-react": "^0.468.0", "next": "13.4.10", "react": "^18.2.0", "react-dom": "^18.2.0", "react-grid-layout": "^1.5.0", "react-pdf": "^9.2.1", "react-resizable": "^3.0.5", "recharts": "^2.15.1", "tailwind-merge": "^2.5.4", "typescript": "^5.1.6", "zod": "^4.0.14"}, "devDependencies": {"@types/node": "20.3.1", "@types/react": "18.2.6", "@types/react-dom": "18.2.4", "@types/react-grid-layout": "^1.3.5", "autoprefixer": "^10.4.20", "eslint": "8.43.0", "eslint-config-next": "^13.4.10", "postcss": "^8.4.49", "tailwindcss": "^3.4.16"}}