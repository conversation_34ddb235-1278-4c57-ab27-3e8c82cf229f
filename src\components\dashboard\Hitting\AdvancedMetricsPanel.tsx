import React from 'react';
import { <PERSON>, CardHeader, CardT<PERSON>le, CardContent } from '@/components/ui/card';
import { <PERSON><PERSON>hart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer } from 'recharts';

interface AdvancedMetricsProps {
    data: HitData[];
    selectedBatter: string;
  }

interface HitData {
    Batter: string;
    ExitSpeed: number;
    Angle: number;
    "Event Date": string;
  }

const AdvancedMetricsPanel = ({ data, selectedBatter }: AdvancedMetricsProps) => {
  // Calculate metrics for each date
  const metrics = React.useMemo(() => {
    const batterData = data.filter((hit: HitData) => hit.Batter === selectedBatter);

    const groupedByDate = batterData.reduce((acc, hit: HitData) => {
      const date = hit["Event Date"] || 'Unknown';
      if (!acc[date]) {
        acc[date] = [];
      }
      acc[date].push(hit);
      return acc;
    }, {} as Record<string, HitData[]>);
    
    // Calculate metrics for each date
    return Object.entries(groupedByDate).map(([date, hits]) => {
      const validHits = hits.filter(hit => hit.ExitSpeed && typeof hit.Angle !== 'undefined');
      const totalHits = validHits.length;
      
      // Line Drive % (hits between 10° and 25° launch angle)
      const lineDrives = validHits.filter(hit => hit.Angle >= 10 && hit.Angle <= 25);
      const lineDrivePercentage = (lineDrives.length / totalHits) * 100;

      // Hard Hit % (hits with exit velocity >= 95 mph)
      const hardHits = validHits.filter(hit => hit.ExitSpeed >= 95);
      const hardHitPercentage = (hardHits.length / totalHits) * 100;

      // Sweet Spot % (hits between 8° and 32° launch angle)
      const sweetSpotHits = validHits.filter(hit => hit.Angle >= 8 && hit.Angle <= 32);
      const sweetSpotPercentage = (sweetSpotHits.length / totalHits) * 100;

      return {
        date,
        lineDrivePercentage,
        hardHitPercentage,
        sweetSpotPercentage,
      };
    }).sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime());
  }, [data, selectedBatter]);

  return (
    <Card className="bg-white backdrop-blur border border-black shadow-lg">
      <CardHeader>
        <CardTitle className="text-black">Advanced Metrics</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="h-[350px]">
          <ResponsiveContainer width="100%" height="100%">
            <LineChart
              data={metrics}
              margin={{ top: 20, right: 30, left: 20, bottom: 20 }}
            >
              <CartesianGrid strokeDasharray="3 3" stroke="#cccccc" />
              <XAxis 
                dataKey="date" 
                tickFormatter={(value) => new Date(value).toLocaleDateString()}
                label={{ value: 'Date', position: 'bottom' }}
              />
              <YAxis 
                domain={[0, 100]}
                label={{ value: 'Percentage (%)', angle: -90, position: 'insideLeft', offset: 0 }}
              />
              <Tooltip
                formatter={(value: number) => `${value.toFixed(1)}%`}
                labelFormatter={(label) => new Date(label).toLocaleDateString()}
              />
              <Line
                type="monotone"
                dataKey="lineDrivePercentage"
                name="Line Drive %"
                stroke="#22c55e"
                strokeWidth={2}
                dot={{ fill: '#22c55e' }}
              />
              <Line
                type="monotone"
                dataKey="hardHitPercentage"
                name="Hard Hit %"
                stroke="#3b82f6"
                strokeWidth={2}
                dot={{ fill: '#3b82f6' }}
              />
              <Line
                type="monotone"
                dataKey="sweetSpotPercentage"
                name="Sweet Spot %"
                stroke="#f59e0b"
                strokeWidth={2}
                dot={{ fill: '#f59e0b' }}
              />
            </LineChart>
          </ResponsiveContainer>
        </div>

        {/* Summary Statistics */}
        <div className="grid grid-cols-3 gap-4 mt-6">
          <div className="text-center">
            <h3 className="text-lg font-semibold text-black">Avg Line Drive %</h3>
            <p className="text-2xl font-bold text-green-600">
              {(metrics.reduce((sum, m) => sum + m.lineDrivePercentage, 0) / metrics.length).toFixed(1)}%
            </p>
          </div>
          <div className="text-center">
            <h3 className="text-lg font-semibold text-black">Avg Hard Hit %</h3>
            <p className="text-2xl font-bold text-blue-600">
              {(metrics.reduce((sum, m) => sum + m.hardHitPercentage, 0) / metrics.length).toFixed(1)}%
            </p>
          </div>
          <div className="text-center">
            <h3 className="text-lg font-semibold text-black">Avg Sweet Spot %</h3>
            <p className="text-2xl font-bold text-amber-600">
              {(metrics.reduce((sum, m) => sum + m.sweetSpotPercentage, 0) / metrics.length).toFixed(1)}%
            </p>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default AdvancedMetricsPanel;