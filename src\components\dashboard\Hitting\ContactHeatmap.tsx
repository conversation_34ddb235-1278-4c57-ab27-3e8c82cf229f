import React, { useMemo, useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON>er, <PERSON><PERSON><PERSON><PERSON>, CardContent } from '@/components/ui/card';

interface HeatmapProps {
  data?: any[];
  selectedBatter?: string;
  selectedLevel?: string;
}

interface HitData {
  Batter: string;
  ExitSpeed: number;
  Angle: number;
  ContactPositionY: number;
}

interface PositionMetrics {
  hits: HitData[];
  avgEV: number;
  avgLA: number;
  qocScore: number;
}

const BENCHMARKS = {
  'Professional': { optimal: 92, min: 88 },
  'College': { optimal: 85, min: 80 },
  'High School': { optimal: 80, min: 75 }
};

// Calculate gaussian function for heat distribution
const gaussian = (x: number, mean: number, sigma: number) => {
  return Math.exp(-Math.pow(x - mean, 2) / (2 * Math.pow(sigma, 2)));
};

// Get color for heat intensity
const getHeatColor = (intensity: number, exitVelo: number, level: string) => {
  const levelBenchmark = BENCHMARKS[level as keyof typeof BENCHMARKS] || BENCHMARKS.College;

  // Calculate normalized score based on level, but ensure we show all data
  const normalizedScore = Math.min(100, Math.max(0,
    ((exitVelo - levelBenchmark.min) / (levelBenchmark.optimal - levelBenchmark.min)) * 100
  ));

  // Always maintain a minimum intensity to ensure visibility
  const baseIntensity = 0.1; // minimum opacity
  const adjustedIntensity = baseIntensity + (intensity * 0.5); // scale the rest of the intensity

  // Return color based on score ranges using softer burnt orange theme
  if (normalizedScore >= 80) {
    return `rgba(229, 90, 43, ${adjustedIntensity})`; // Softer burnt orange for excellent
  } else if (normalizedScore >= 60) {
    return `rgba(255, 140, 90, ${adjustedIntensity})`; // Lighter orange for good
  } else {
    return `rgba(255, 107, 107, ${adjustedIntensity})`; // Softer red for poor
  }
};

const ContactHeatmap: React.FC<HeatmapProps> = ({
  data = [],
  selectedBatter = '',
  selectedLevel = 'College'
}) => {
  const [hoveredPosition, setHoveredPosition] = useState<string | null>(null);
  const [tooltipPosition, setTooltipPosition] = useState({ x: 0, y: 0 });
  const [tooltipData, setTooltipData] = useState<any>(null);
  // Filter data for selected batter
  const batterData = useMemo(() =>
    selectedBatter ? data.filter(hit => hit.Batter === selectedBatter) : [],
    [data, selectedBatter]
  );

  // Calculate metrics for visualization
  const metrics = useMemo(() => {
    if (!batterData.length) return {};

    const grouped = batterData.reduce<Record<string, PositionMetrics>>((acc, hit) => {
      if (!hit.ContactPositionY) return acc;

      const position = Math.round(hit.ContactPositionY * 10) / 10;
      console.log('Raw ContactPositionY:', hit.ContactPositionY);
      console.log('Rounded position:', position);
      console.log('Calculated y coordinate:', 70 - (position * 15));
      if (!acc[position]) {
        acc[position] = {
          hits: [],
          avgEV: 0,
          avgLA: 0,
          qocScore: 0
        };
      }
      acc[position].hits.push(hit);
      return acc;
    }, {});

    // Calculate averages for each position
    Object.keys(grouped).forEach(pos => {
      const hits = grouped[pos].hits;
      grouped[pos].avgEV = hits.reduce((sum, hit) => sum + (hit.ExitSpeed || 0), 0) / hits.length;
      grouped[pos].avgLA = hits.reduce((sum, hit) => sum + (hit.Angle || 0), 0) / hits.length;
      // Using existing QoC calculation
      grouped[pos].qocScore = hits.reduce((sum, hit) => sum + ((hit.ExitSpeed > 95 ? 100 : hit.ExitSpeed / 95 * 100) || 0), 0) / hits.length;
    });

    return grouped;
  }, [batterData]);

  const statistics = useMemo(() => {
    if (!batterData.length) return { avgEV: 0, avgLA: 0, barrelCount: 0 };

    const validHits = batterData.filter(hit => hit.ExitSpeed && hit.Angle);
    const levelOptimal = BENCHMARKS[selectedLevel as keyof typeof BENCHMARKS]?.optimal || 85;

    return {
      avgEV: validHits.reduce((sum, hit) => sum + hit.ExitSpeed, 0) / validHits.length,
      avgLA: validHits.reduce((sum, hit) => sum + hit.Angle, 0) / validHits.length,
      barrelCount: validHits.filter(hit => hit.ExitSpeed >= levelOptimal).length
    };
  }, [batterData, selectedLevel]);

  if (!selectedBatter) {
    return (
      <div className="w-full h-96 flex items-center justify-center text-white">
        Select a batter to view contact point analysis
      </div>
    );
  }

  return (
    <div className="w-full h-full relative">
      {/* Gradient Legend */}
      <div className="absolute middle-4 right-4 flex items-center gap-2">
        <div className="h-4 w-20 bg-gradient-to-r from-red-500 via-orange-400 to-orange-600" />
        <div className="text-xs text-white">
          <span>Quality</span>
        </div>
      </div>

      {/* Main Visualization */}
      <div className="relative h-full w-full">
        <svg viewBox="-10 14 120 100" preserveAspectRatio="xMidYMid meet" className="w-full h-[350px]">
          {/* Home Plate */}
          <g transform="translate(2, 15)">
            <path
              d="M35,40 L65,40 L65,55 L50,70 L35,55 Z"
              fill="none"
              stroke="white"
              strokeWidth="0.5"
            />
            <circle cx="35" cy="40" r="0.75" fill="white" />
            <circle cx="65" cy="40" r="0.75" fill="white" />
            <circle cx="65" cy="55" r="0.75" fill="white" />
            <circle cx="50" cy="70" r="0.75" fill="white" />
            <circle cx="35" cy="55" r="0.75" fill="white" />
          </g>

          {/* Y-axis and grid */}
          <g transform="translate(-35, 15)">
          {[-1, 0, 1, 2, 3, 4,].map((value) => (
              <React.Fragment key={`grid-${value}`}>
                <line
                  x1="30" y1={70 - (value * 15)} x2="120" y2={70 - (value * 15)}
                  stroke="white" strokeWidth="0.5" strokeDasharray="2"
                  opacity={0.3}
                />
                <text
                  x="25" y={70 - (value * 15)}
                  fontSize="3.5" textAnchor="end" dominantBaseline="middle"
                  fill="white"
                >
                  {value}ft
                </text>
              </React.Fragment>
            ))}
            <line x1="30" y1="0" x2="30" y2="100" stroke="white" strokeWidth="0.5" />

            console.log('Position in feet:', positionInFeet);
            console.log('Raw y before calculation:', 70);
            console.log('Scaling factor:', 15);
            console.log('Calculated y:', 70 - (positionInFeet * 15));

            {/* Heat Map Layer */}
            <g>
              {Object.entries(metrics).map(([position, data], i) => {
                const positionInFeet = Number(position);
                // Scale the position to our SVG coordinate system
                // 70 is home plate tip, 15 units per foot, moving up reduces y value
                const y = 70 - (positionInFeet * 16);
                // Add 5 to center the heat band
                const adjustedY = y + 2;

                return (
                  <g key={i}>
                    {/* Highlight band for hovered position */}
                    {hoveredPosition === position && (
                      <rect
                        x={30}
                        y={adjustedY - 1}
                        width={100}
                        height={5.5}
                        fill="rgba(229, 90, 43, 0.2)"
                        stroke="#e55a2b"
                        strokeWidth="1"
                        strokeDasharray="2,2"
                        className="animate-pulse"
                      />
                    )}

                    {Array.from({ length: 50 }).map((_, idx) => {
                      const x = 30 + idx * 2;
                      const heatValue = gaussian(x, 75, 30) * (data.qocScore / 100);

                      const isHovered = hoveredPosition === position;
                      const baseOpacity = 0.3;
                      const hoverOpacity = 0.7;
                      const currentOpacity = isHovered ? hoverOpacity : baseOpacity;

                      return (
                        <rect
                          key={idx}
                          x={x}
                          y={adjustedY}
                          width="2"
                          height="3.5"
                          fill={getHeatColor(heatValue, data.avgEV, selectedLevel)}
                          opacity={currentOpacity}
                          className={`transition-all duration-200 ease-in-out cursor-pointer ${
                            isHovered ? 'filter brightness-150 drop-shadow-lg' : 'hover:brightness-110'
                          }`}
                          style={{
                            transform: isHovered ? 'scale(1.05)' : 'scale(1)',
                            transformOrigin: 'center',
                            filter: isHovered ? 'drop-shadow(0 0 4px rgba(229, 90, 43, 0.8))' : 'none'
                          }}
                          onMouseEnter={(e) => {
                            setHoveredPosition(position);
                            const svgRect = e.currentTarget.closest('svg')?.getBoundingClientRect();
                            const rect = (e.target as HTMLElement).getBoundingClientRect();
                            const relativeX = rect.left - (svgRect?.left || 0) + rect.width;
                            const relativeY = rect.top - (svgRect?.top || 0);

                            setTooltipPosition({ x: relativeX + 10, y: relativeY });
                            setTooltipData({
                              position,
                              avgEV: data.avgEV,
                              qocScore: data.qocScore,
                              sampleSize: data.hits.length,
                              avgLA: data.avgLA
                            });
                          }}
                          onMouseMove={(e) => {
                            const svgRect = e.currentTarget.closest('svg')?.getBoundingClientRect();
                            const rect = (e.target as HTMLElement).getBoundingClientRect();
                            const relativeX = rect.left - (svgRect?.left || 0) + rect.width;
                            const relativeY = rect.top - (svgRect?.top || 0);

                            setTooltipPosition({ x: relativeX + 10, y: relativeY });
                          }}
                          onMouseLeave={() => {
                            setHoveredPosition(null);
                            setTooltipData(null);
                          }}
                        />
                      );
                    })}
                  </g>
                );
              })}
            </g>
          </g>
        </svg>

        {/* Stats Section */}
        <div className="mt-4 flex justify-around text-center">
          <div>
            <div className="text-sm font-medium text-gray-400">Avg Exit Velo</div>
            <div className={`text-lg font-bold ${
              statistics.avgEV >= BENCHMARKS[selectedLevel as keyof typeof BENCHMARKS]?.optimal ? 'text-[#e55a2b]' :
              statistics.avgEV >= BENCHMARKS[selectedLevel as keyof typeof BENCHMARKS]?.min ? 'text-[#ff8c5a]' :
              'text-red-400'
            }`}>
              {statistics.avgEV.toFixed(1)} mph
            </div>
          </div>
          <div>
            <div className="text-sm font-medium text-gray-400">Avg Launch Angle</div>
            <div className={`text-lg font-bold ${
              (statistics.avgLA >= 10 && statistics.avgLA <= 25) ? 'text-[#e55a2b]' :
              (statistics.avgLA >= 5 && statistics.avgLA <= 30) ? 'text-[#ff8c5a]' :
              'text-red-400'
            }`}>
              {statistics.avgLA.toFixed(1)}°
            </div>
          </div>
          <div>
            <div className="text-sm font-medium text-gray-400">Barrels</div>
            <div className="text-lg font-bold text-[#e55a2b]">
              {statistics.barrelCount}
            </div>
          </div>
        </div>
      </div>
      {/* Enhanced Tooltip */}
      {tooltipData && (
        <div
          className="absolute bg-gray-900/95 backdrop-blur-sm border-2 border-[#e55a2b] rounded-lg shadow-2xl shadow-[#e55a2b]/30 text-white z-50 pointer-events-none transform transition-all duration-200 ease-out"
          style={{
            left: `${tooltipPosition.x}px`,
            top: `${tooltipPosition.y}px`,
            transform: 'translate(-50%, -100%) scale(1.05)',
            animation: 'fadeInScale 200ms ease-out'
          }}
        >
          <div className="p-4 min-w-[200px]">
            <div className="flex items-center gap-2 mb-3">
              <div className="w-3 h-3 rounded-full bg-[#e55a2b] shadow-lg shadow-[#e55a2b]/50"></div>
              <h4 className="font-bold text-lg text-[#e55a2b]">Contact Point</h4>
            </div>

            <div className="space-y-2">
              <div className="flex justify-between items-center">
                <span className="text-gray-300 text-sm">Position:</span>
                <span className="font-semibold text-white">{tooltipData.position}ft</span>
              </div>

              <div className="flex justify-between items-center">
                <span className="text-gray-300 text-sm">Exit Velocity:</span>
                <span className="font-semibold text-[#e55a2b]">{tooltipData.avgEV.toFixed(1)} mph</span>
              </div>

              <div className="flex justify-between items-center">
                <span className="text-gray-300 text-sm">Launch Angle:</span>
                <span className="font-semibold text-[#ff8c5a]">{tooltipData.avgLA.toFixed(1)}°</span>
              </div>

              <div className="flex justify-between items-center">
                <span className="text-gray-300 text-sm">Quality Score:</span>
                <span className="font-semibold text-white">{tooltipData.qocScore.toFixed(1)}</span>
              </div>

              <div className="border-t border-gray-700 pt-2 mt-3">
                <div className="flex justify-between items-center">
                  <span className="text-gray-400 text-xs">Sample Size:</span>
                  <span className="font-medium text-gray-200">{tooltipData.sampleSize} hits</span>
                </div>
              </div>
            </div>
          </div>

          {/* Tooltip Arrow */}
          <div className="absolute top-full left-1/2 transform -translate-x-1/2">
            <div className="w-0 h-0 border-l-8 border-r-8 border-t-8 border-l-transparent border-r-transparent border-t-[#e55a2b]"></div>
          </div>
        </div>
      )}
    </div>
  );
};

export default ContactHeatmap;