import React, { useState, useMemo } from 'react';
import { <PERSON>, CardHeader, CardT<PERSON>le, CardContent } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { <PERSON><PERSON><PERSON>, Line, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer, Brush } from 'recharts';
import {
  calculateSinglePitchScore,
  calculateArsenalScore,
  LEVEL_BENCHMARKS,
} from '@/components/dashboard/PitchingMetricsDashboard';

type MetricType = 'arsenalScore' | 'velocity' | 'movement' | 'control';

type Level = 'Professional' | 'College' | 'High School';
type PitchType = keyof typeof LEVEL_BENCHMARKS[Level];

interface PitchingTrendAnalysisProps {
  data: Array<{
    Pitcher: string;
    AutoPitchType: PitchType;
    RelSpeed: number;
    SpinRate: number;
    VertBreak: number;
    HorzBreak: number;
    RelSide: number;
    RelHeight: number;
    Extension: number;
    Level: Level;
    EventID?: string;
    EventDate?: string;
  }>;
  selectedPitcher: string;
  selectedLevel: Level;
  timeRange?: string;
}

interface Metric {
  date: string;
  arsenalScore: number;
  velocityScore: number;
  movementScore: number;
  controlScore: number;
  pitches: number;
}

const PitchingTrendAnalysis: React.FC<PitchingTrendAnalysisProps> = ({
  data,
  selectedPitcher,
  selectedLevel,
  timeRange,
}) => {
  const [selectedTimeRange, setSelectedTimeRange] = useState<string>('30');
  const [selectedMetric, setSelectedMetric] = useState<string>('all');

  // --- Data Validation: Filter out rows with missing or invalid EventDate ---
  const validatedData = useMemo(() => {
    return data.filter((pitch) => {
      const eventDate = pitch.EventDate;
      if (!eventDate) return false;
      const dt = new Date(eventDate);
      return !isNaN(dt.getTime());
    });
  }, [data]);

  // --- Helper functions for averaging scores ---
  const calculateAverageVelocityScore = (pitches: any[], level: string) => {
    return (
      pitches.reduce((sum, pitch) => {
        const benchmark =
          LEVEL_BENCHMARKS[level as keyof typeof LEVEL_BENCHMARKS]?.[
            pitch.AutoPitchType as keyof typeof LEVEL_BENCHMARKS[typeof level]
          ];
        if (!benchmark) return sum;
        const score = calculateSinglePitchScore(pitch, benchmark, pitches);
        return sum + score.velocityScore;
      }, 0) / pitches.length
    );
  };

  const calculateAverageMovementScore = (pitches: any[], level: string) => {
    return (
      pitches.reduce((sum, pitch) => {
        const benchmark =
          LEVEL_BENCHMARKS[level as keyof typeof LEVEL_BENCHMARKS]?.[
            pitch.AutoPitchType as keyof typeof LEVEL_BENCHMARKS[typeof level]
          ];
        if (!benchmark) return sum;
        const score = calculateSinglePitchScore(pitch, benchmark, pitches);
        return sum + score.movementScore;
      }, 0) / pitches.length
    );
  };

  const calculateAverageControlScore = (pitches: any[], level: string) => {
    return (
      pitches.reduce((sum, pitch) => {
        const benchmark =
          LEVEL_BENCHMARKS[level as keyof typeof LEVEL_BENCHMARKS]?.[
            pitch.AutoPitchType as keyof typeof LEVEL_BENCHMARKS[typeof level]
          ];
        if (!benchmark) return sum;
        const score = calculateSinglePitchScore(pitch, benchmark, pitches);
        return sum + score.releaseScore;
      }, 0) / pitches.length
    );
  };

  const metrics = useMemo<Metric[]>(() => {
    // Filter by selected pitcher and then by date range.
    const pitcherData = validatedData
      .filter((pitch) => {
        // Filter by selected pitcher.
        if (pitch.Pitcher !== selectedPitcher) return false;

        // If no time range is specified (or "all" is chosen), keep the pitch.
        if (!selectedTimeRange || selectedTimeRange === 'all') return true;

        const pitchDate = new Date(pitch.EventDate!);
        // Get valid time values from the validated dataset.
        const validTimes = validatedData.map((p) => new Date(p.EventDate!).getTime());
        if (validTimes.length === 0) return false;
        const latestDate = new Date(Math.max(...validTimes));
        const daysDiff = Math.floor(
          (latestDate.getTime() - pitchDate.getTime()) / (1000 * 60 * 60 * 24)
        );
        const withinRange = daysDiff <= parseInt(selectedTimeRange);
        return withinRange;
      })
      .filter((pitch) => pitch.Pitcher === selectedPitcher)
      .map((pitch) => ({ ...pitch, Level: selectedLevel }));

    if (pitcherData.length === 0) {
      return [];
    }

    // Group pitches by EventDate.
    const groupedByDate = pitcherData.reduce((acc, pitch) => {
      const date = pitch.EventDate || 'Unknown';
      if (!acc[date]) acc[date] = [];
      acc[date].push(pitch);
      return acc;
    }, {} as Record<string, typeof pitcherData>);

    const result = Object.entries(groupedByDate)
      .map(([date, pitches]) => {
        const validPitches = pitches.filter(
          (pitch) => pitch.RelSpeed && pitch.SpinRate && pitch.VertBreak && pitch.HorzBreak
        );

        if (validPitches.length === 0) return null;

        const arsenalScore = calculateArsenalScore(validPitches);

        return {
          date,
          arsenalScore,
          velocityScore: calculateAverageVelocityScore(validPitches, selectedLevel),
          movementScore: calculateAverageMovementScore(validPitches, selectedLevel),
          controlScore: calculateAverageControlScore(validPitches, selectedLevel),
          pitches: validPitches.length,
        };
      })
      .filter((item): item is NonNullable<typeof item> => item !== null)
      .sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime());

    return result;
  }, [validatedData, selectedPitcher, selectedLevel, selectedTimeRange]);

  // If there are no metrics (after filtering), show a friendly message.
  if (metrics.length === 0) {
    return (
      <div className="w-full p-4 text-center">
        <p>No valid pitching data available for the selected criteria.</p>
      </div>
    );
  }

  return (
    <div className="w-full">
      <div className="mb-4 flex justify-between items-center">
        <div className="flex gap-4">
          <Select value={selectedTimeRange} onValueChange={setSelectedTimeRange}>
            <SelectTrigger className="w-[180px] bg-[rgba(15,15,35,0.8)] text-white border border-[#e55a2b] shadow-lg shadow-[#e55a2b]/20">
              <SelectValue placeholder="Time Range" />
            </SelectTrigger>
            <SelectContent className="bg-[rgba(15,15,35,0.8)] text-white border border-[#e55a2b] shadow-lg">
              <SelectItem value="7">Last 7 Days</SelectItem>
              <SelectItem value="30">Last 30 Days</SelectItem>
              <SelectItem value="90">Last 90 Days</SelectItem>
              <SelectItem value="all">All Time</SelectItem>
            </SelectContent>
          </Select>

          <Select value={selectedMetric} onValueChange={setSelectedMetric}>
            <SelectTrigger className="w-[180px] bg-[rgba(15,15,35,0.8)] text-white border border-[#e55a2b] shadow-lg shadow-[#e55a2b]/20">
              <SelectValue placeholder="Select Metric" />
            </SelectTrigger>
            <SelectContent className="bg-[rgba(15,15,35,0.8)] text-white border border-[#e55a2b] shadow-lg">
              <SelectItem value="all">All Metrics</SelectItem>
              <SelectItem value="arsenalScore">Arsenal Score</SelectItem>
              <SelectItem value="velocity">Velocity</SelectItem>
              <SelectItem value="movement">Movement</SelectItem>
              <SelectItem value="control">Control</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>
      <div className="h-[600px]">
        <ResponsiveContainer>
          <LineChart data={metrics} margin={{ top: 20, right: 30, left: 20, bottom: 50 }}>
            <CartesianGrid strokeDasharray="3 3" stroke="#ffffff" />
            <XAxis
              dataKey="date"
              tickFormatter={(value) => new Date(value).toLocaleDateString()}
              label={{ value: 'Date', position: 'bottom', offset: 0 }}
              stroke="#ffffff"
            />
            <YAxis
              domain={[0, 100]}
              label={{ value: 'Score', angle: -90, position: 'insideLeft', offset: 10 }}
              stroke="#ffffff"
            />
            <Tooltip
              formatter={(value: number) => `${value.toFixed(1)}`}
              labelFormatter={(label) => new Date(label).toLocaleDateString()}
              contentStyle={{
                backgroundColor: 'rgba(15, 15, 35, 0.9)',
                border: '1px solid #e55a2b',
                borderRadius: '8px',
                color: '#ffffff'
              }}
            />
            {/* Add Brush for interactive zooming */}
            <Brush dataKey="date" height={30} stroke="#e55a2b" />
            <Line
              type="monotone"
              dataKey="arsenalScore"
              name="Arsenal Score"
              stroke="#22c55e"
              strokeWidth={2}
              dot={{ fill: '#22c55e' }}
              hide={selectedMetric !== 'all' && selectedMetric !== 'arsenalScore'}
            />
            <Line
              type="monotone"
              dataKey="velocityScore"
              name="Velocity Score"
              stroke="#3b82f6"
              strokeWidth={2}
              dot={{ fill: '#3b82f6' }}
              hide={selectedMetric !== 'all' && selectedMetric !== 'velocity'}
            />
            <Line
              type="monotone"
              dataKey="movementScore"
              name="Movement Score"
              stroke="#f59e0b"
              strokeWidth={2}
              dot={{ fill: '#f59e0b' }}
              hide={selectedMetric !== 'all' && selectedMetric !== 'movement'}
            />
            <Line
              type="monotone"
              dataKey="controlScore"
              name="Control Score"
              stroke="#ec4899"
              strokeWidth={2}
              dot={{ fill: '#ec4899' }}
              hide={selectedMetric !== 'all' && selectedMetric !== 'control'}
            />
          </LineChart>
        </ResponsiveContainer>
      </div>
    </div>
  );
};

export default PitchingTrendAnalysis;

