/**
 * Genkit flows for player analysis
 * Implements AI-powered analytics as described in handoff document
 */

import { defineFlow, runFlow } from '@genkit-ai/flow';
import { gemini15Flash } from '@genkit-ai/googleai';
import { z } from 'zod';

// Input/Output schemas using Zod for type safety
const PlayerAnalysisInput = z.object({
  playerId: z.string(),
  playerName: z.string(),
  stats: z.object({
    hitting: z.object({
      avgExitVelocity: z.number().optional(),
      avgLaunchAngle: z.number().optional(),
      qualityOfContactScore: z.number().optional(),
      totalHits: z.number().optional(),
    }).optional(),
    pitching: z.object({
      avgVelocity: z.number().optional(),
      arsenalScore: z.number().optional(),
      totalPitches: z.number().optional(),
    }).optional(),
  }),
  level: z.enum(['Professional', 'College', 'High School']),
  question: z.string(),
});

const PlayerAnalysisOutput = z.object({
  text: z.string(),
  chart: z.object({
    type: z.enum(['bar', 'line', 'scatter', 'pie']),
    data: z.array(z.number()),
    labels: z.array(z.string()),
    title: z.string().optional(),
    xAxisLabel: z.string().optional(),
    yAxisLabel: z.string().optional(),
  }).optional(),
  insights: z.array(z.string()).optional(),
  recommendations: z.array(z.string()).optional(),
});

/**
 * Player Analysis Flow
 * Analyzes individual player performance and provides insights
 */
export const playerAnalysisFlow = defineFlow(
  {
    name: 'playerAnalysis',
    inputSchema: PlayerAnalysisInput,
    outputSchema: PlayerAnalysisOutput,
  },
  async (input) => {
    const { playerName, stats, level, question } = input;
    
    // Build context for the AI
    const context = buildPlayerContext(playerName, stats, level);
    
    // Create the prompt
    const prompt = `
You are a professional baseball analytics expert. Analyze the following player data and answer the question.

Player: ${playerName}
Level: ${level}
Data: ${context}

Question: ${question}

Provide a comprehensive analysis including:
1. Direct answer to the question
2. Key insights about the player's performance
3. Recommendations for improvement
4. If applicable, suggest a chart visualization

Respond in JSON format with the following structure:
{
  "text": "Your detailed analysis here",
  "chart": {
    "type": "bar|line|scatter|pie",
    "data": [numbers],
    "labels": ["labels"],
    "title": "Chart title",
    "xAxisLabel": "X axis label",
    "yAxisLabel": "Y axis label"
  },
  "insights": ["insight 1", "insight 2"],
  "recommendations": ["recommendation 1", "recommendation 2"]
}
`;

    try {
      const response = await gemini15Flash.generate({
        prompt,
        config: {
          temperature: 0.7,
          maxOutputTokens: 1000,
        },
      });

      // Parse the response
      const result = JSON.parse(response.text());
      
      return {
        text: result.text || 'Analysis completed.',
        chart: result.chart,
        insights: result.insights,
        recommendations: result.recommendations,
      };
    } catch (error) {
      console.error('Error in player analysis flow:', error);
      return {
        text: `I apologize, but I encountered an error analyzing ${playerName}'s performance. Please try again.`,
      };
    }
  }
);

/**
 * Build context string from player stats
 */
function buildPlayerContext(playerName: string, stats: any, level: string): string {
  let context = `${playerName} is a ${level} level player.\n\n`;
  
  if (stats.hitting) {
    context += 'Hitting Statistics:\n';
    if (stats.hitting.avgExitVelocity) {
      context += `- Average Exit Velocity: ${stats.hitting.avgExitVelocity} mph\n`;
    }
    if (stats.hitting.avgLaunchAngle) {
      context += `- Average Launch Angle: ${stats.hitting.avgLaunchAngle}°\n`;
    }
    if (stats.hitting.qualityOfContactScore) {
      context += `- Quality of Contact Score: ${stats.hitting.qualityOfContactScore}/100\n`;
    }
    if (stats.hitting.totalHits) {
      context += `- Total Hits Analyzed: ${stats.hitting.totalHits}\n`;
    }
    context += '\n';
  }
  
  if (stats.pitching) {
    context += 'Pitching Statistics:\n';
    if (stats.pitching.avgVelocity) {
      context += `- Average Velocity: ${stats.pitching.avgVelocity} mph\n`;
    }
    if (stats.pitching.arsenalScore) {
      context += `- Arsenal Score: ${stats.pitching.arsenalScore}/100\n`;
    }
    if (stats.pitching.totalPitches) {
      context += `- Total Pitches Analyzed: ${stats.pitching.totalPitches}\n`;
    }
    context += '\n';
  }
  
  return context;
}

/**
 * Baseline Score Calculation Flow
 * Calculates the proprietary AI-driven baseline score (0-100)
 */
const BaselineScoreInput = z.object({
  playerId: z.string(),
  playerName: z.string(),
  stats: z.object({
    hitting: z.any().optional(),
    pitching: z.any().optional(),
  }),
  level: z.enum(['Professional', 'College', 'High School']),
});

const BaselineScoreOutput = z.object({
  baselineScore: z.number().min(0).max(100),
  breakdown: z.object({
    hitting: z.number().optional(),
    pitching: z.number().optional(),
    overall: z.number(),
  }),
  explanation: z.string(),
});

export const baselineScoreFlow = defineFlow(
  {
    name: 'baselineScore',
    inputSchema: BaselineScoreInput,
    outputSchema: BaselineScoreOutput,
  },
  async (input) => {
    const { playerName, stats, level } = input;
    
    // Calculate baseline score using AI-driven analysis
    const prompt = `
Calculate a comprehensive baseline score (0-100) for ${playerName}, a ${level} level player.

Player Data:
${JSON.stringify(stats, null, 2)}

Consider:
1. Performance relative to ${level} benchmarks
2. Consistency across metrics
3. Areas of strength and weakness
4. Overall player profile

Provide a JSON response with:
{
  "baselineScore": 85,
  "breakdown": {
    "hitting": 82,
    "pitching": 88,
    "overall": 85
  },
  "explanation": "Detailed explanation of the score calculation"
}
`;

    try {
      const response = await gemini15Flash.generate({
        prompt,
        config: {
          temperature: 0.3, // Lower temperature for more consistent scoring
          maxOutputTokens: 500,
        },
      });

      const result = JSON.parse(response.text());
      
      return {
        baselineScore: Math.round(result.baselineScore),
        breakdown: result.breakdown,
        explanation: result.explanation,
      };
    } catch (error) {
      console.error('Error in baseline score flow:', error);
      
      // Fallback calculation
      let score = 50; // Base score
      if (stats.hitting?.qualityOfContactScore) {
        score += (stats.hitting.qualityOfContactScore - 50) * 0.3;
      }
      if (stats.pitching?.arsenalScore) {
        score += (stats.pitching.arsenalScore - 50) * 0.3;
      }
      
      return {
        baselineScore: Math.max(0, Math.min(100, Math.round(score))),
        breakdown: {
          overall: Math.round(score),
        },
        explanation: 'Score calculated using fallback algorithm due to AI service unavailability.',
      };
    }
  }
);
