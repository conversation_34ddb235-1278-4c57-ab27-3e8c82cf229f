"use client"

import React, { useState } from 'react';
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { ScoreGauge } from './PitchingMetricsDashboard';
import { 
  calculateSinglePitchScore,
  LEVEL_BENCHMARKS,
  PitchTypes
} from './PitchingMetricsDashboard';
import { PitchData } from './PitchingMetricsDashboard';

interface CoachDashboardProps {
  data: any[];
  selectedLevel: 'Professional' | 'College' | 'High School';
  calculatePitcherMetrics: (pitcherData: PitchData[]) => {
    arsenalScore: number;
    arsenalMetrics: Record<string, any>;
  };
}

const CoachDashboard: React.FC<CoachDashboardProps> = ({ 
  data, 
  selectedLevel, 
  calculatePitcherMetrics 
}) => {
  const [sortOrder, setSortOrder] = useState<'highest' | 'lowest'>('highest');
  const uniquePitchers = [...new Set(data.map(pitch => pitch.Pitcher))];

  // Calculate all pitcher scores first
  const pitcherScores = uniquePitchers.map(pitcher => {
    const pitcherData = data.filter(pitch => pitch.Pitcher === pitcher);
    const { arsenalScore } = calculatePitcherMetrics(pitcherData);
    return { pitcher, arsenalScore };
  });

  // Sort based on selected order
  const sortedPitchers = [...pitcherScores].sort((a, b) => {
    return sortOrder === 'highest' 
      ? b.arsenalScore - a.arsenalScore 
      : a.arsenalScore - b.arsenalScore;
  });

  return (
    <Card className="bg-white/80 backdrop-blur shadow-lg">
      <CardHeader>
        <div className="flex justify-between items-center">
          <CardTitle className="text-black">Coach's Dashboard</CardTitle>
          <Select
            value={sortOrder}
            onValueChange={(value: 'highest' | 'lowest') => setSortOrder(value)}
          >
            <SelectTrigger className="w-[180px] bg-white/80 text-black border-black/20">
              <SelectValue placeholder="Sort by score" />
            </SelectTrigger>
            <SelectContent className="text-black">
              <SelectItem value="highest">Highest Score First</SelectItem>
              <SelectItem value="lowest">Lowest Score First</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {sortedPitchers.map(({ pitcher }) => {
            const pitcherData = data.filter(pitch => pitch.Pitcher === pitcher);
            const { arsenalScore, arsenalMetrics } = calculatePitcherMetrics(pitcherData);
            
            return (
              <div key={pitcher} className="p-4 border rounded bg-white hover:bg-gray-50">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-4">
                    <h3 className="text-lg font-semibold text-black">{pitcher}</h3>
                    <ScoreGauge
                      score={arsenalScore}
                      size="md"
                      label="Arsenal"
                    />
                  </div>
                  <div className="flex gap-2">
                    {Object.entries(arsenalMetrics).map(([type, data]: [string, any]) => (
                      <div key={type} className="text-center">
                        <ScoreGauge
                          score={data.score.totalScore}
                          size="sm"
                          label={type}
                        />
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            );
          })}
        </div>
      </CardContent>
    </Card>
  );
};

export default CoachDashboard;