"use client";

import React, { useState, useEffect } from 'react';
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, LineChart, Line } from 'recharts';
import { Users, TrendingUp, Target, CircleDot, Upload } from 'lucide-react';
import MainLayout from '@/components/layout/MainLayout';
import { getStorageData, STORAGE_KEYS } from '@/lib/storage';
import { HittingData } from '@/components/dashboard/Hitting/HittingMetricsDashboard';
import { PitchData } from '@/components/dashboard/PitchingMetricsDashboard';

interface FacilityStats {
  totalPlayers: number;
  totalSessions: number;
  avgPerformanceScore: number;
  topPerformers: Array<{
    name: string;
    score: number;
    type: 'hitting' | 'pitching';
  }>;
}

export default function FacilityDashboard() {
  const [facilityStats, setFacilityStats] = useState<FacilityStats>({
    totalPlayers: 0,
    totalSessions: 0,
    avgPerformanceScore: 0,
    topPerformers: []
  });
  const [selectedLevel, setSelectedLevel] = useState<'Professional' | 'College' | 'High School'>('Professional');
  const [hittingData, setHittingData] = useState<HittingData[]>([]);
  const [pitchingData, setPitchingData] = useState<PitchData[]>([]);

  useEffect(() => {
    // Load data from localStorage
    const hitting: HittingData[] = getStorageData(STORAGE_KEYS.HITTING_DATA, []);
    const pitching: PitchData[] = getStorageData(STORAGE_KEYS.PITCHING_DATA, []);
    
    setHittingData(hitting);
    setPitchingData(pitching);
    
    // Calculate facility stats
    calculateFacilityStats(hitting, pitching);
  }, [selectedLevel]);

  const calculateFacilityStats = (hitting: HittingData[], pitching: PitchData[]) => {
    // Get unique players
    const hitters = new Set(hitting.map(h => h.Batter));
    const pitchers = new Set(pitching.map(p => p.Pitcher));
    const allPlayers = new Set([...hitters, ...pitchers]);
    
    // Calculate sessions (simplified - using unique event IDs)
    const hittingSessions = new Set(hitting.map(h => h.EventID).filter(Boolean));
    const pitchingSessions = new Set(pitching.map(p => p.EventID).filter(Boolean));
    const totalSessions = hittingSessions.size + pitchingSessions.size;
    
    // Calculate top performers (simplified scoring)
    const topPerformers: Array<{name: string; score: number; type: 'hitting' | 'pitching'}> = [];
    
    // Top hitters by exit velocity
    hitters.forEach(hitter => {
      const playerHits = hitting.filter(h => h.Batter === hitter);
      const avgExitVelo = playerHits.reduce((sum, hit) => sum + hit.ExitSpeed, 0) / playerHits.length;
      topPerformers.push({
        name: hitter,
        score: Math.round(avgExitVelo),
        type: 'hitting'
      });
    });
    
    // Top pitchers by velocity
    pitchers.forEach(pitcher => {
      const playerPitches = pitching.filter(p => p.Pitcher === pitcher);
      const avgVelo = playerPitches.reduce((sum, pitch) => sum + pitch.RelSpeed, 0) / playerPitches.length;
      topPerformers.push({
        name: pitcher,
        score: Math.round(avgVelo),
        type: 'pitching'
      });
    });
    
    // Sort and take top 5
    topPerformers.sort((a, b) => b.score - a.score);
    
    setFacilityStats({
      totalPlayers: allPlayers.size,
      totalSessions,
      avgPerformanceScore: topPerformers.length > 0 
        ? Math.round(topPerformers.reduce((sum, p) => sum + p.score, 0) / topPerformers.length)
        : 0,
      topPerformers: topPerformers.slice(0, 5)
    });
  };

  // Sample data for charts
  const performanceTrend = [
    { month: 'Jan', hitting: 85, pitching: 82 },
    { month: 'Feb', hitting: 87, pitching: 84 },
    { month: 'Mar', hitting: 89, pitching: 86 },
    { month: 'Apr', hitting: 91, pitching: 88 },
    { month: 'May', hitting: 93, pitching: 90 },
    { month: 'Jun', hitting: 95, pitching: 92 },
  ];

  return (
    <MainLayout>
      <div className="p-6 space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-white">Facility Dashboard</h1>
            <p className="text-gray-400 mt-2">High-level overview aggregating all player data</p>
          </div>
          <div className="flex items-center space-x-4">
            <Select value={selectedLevel} onValueChange={(value: any) => setSelectedLevel(value)}>
              <SelectTrigger className="w-48 bg-gray-800 border-gray-600 text-white">
                <SelectValue />
              </SelectTrigger>
              <SelectContent className="bg-gray-800 border-gray-600">
                <SelectItem value="Professional">Professional</SelectItem>
                <SelectItem value="College">College</SelectItem>
                <SelectItem value="High School">High School</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <Card className="floating-card">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-400">Total Players</p>
                  <p className="text-3xl font-bold text-white">{facilityStats.totalPlayers}</p>
                </div>
                <Users className="h-8 w-8 text-orange-500" />
              </div>
            </CardContent>
          </Card>

          <Card className="floating-card">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-400">Total Sessions</p>
                  <p className="text-3xl font-bold text-white">{facilityStats.totalSessions}</p>
                </div>
                <TrendingUp className="h-8 w-8 text-orange-500" />
              </div>
            </CardContent>
          </Card>

          <Card className="floating-card">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-400">Avg Performance</p>
                  <p className="text-3xl font-bold text-white">{facilityStats.avgPerformanceScore}</p>
                </div>
                <Target className="h-8 w-8 text-orange-500" />
              </div>
            </CardContent>
          </Card>

          <Card className="floating-card">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-400">Active Level</p>
                  <p className="text-lg font-bold text-white">{selectedLevel}</p>
                </div>
                <CircleDot className="h-8 w-8 text-orange-500" />
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Charts */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Performance Trend */}
          <Card className="floating-card">
            <CardHeader>
              <CardTitle className="text-white">Performance Trends</CardTitle>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={300}>
                <LineChart data={performanceTrend}>
                  <CartesianGrid strokeDasharray="3 3" stroke="rgba(255,255,255,0.1)" />
                  <XAxis dataKey="month" stroke="#ffffff" />
                  <YAxis stroke="#ffffff" />
                  <Tooltip 
                    contentStyle={{ 
                      backgroundColor: '#1a1a2e', 
                      border: '1px solid #e55a2b',
                      borderRadius: '8px',
                      color: '#ffffff'
                    }} 
                  />
                  <Line type="monotone" dataKey="hitting" stroke="#e55a2b" strokeWidth={2} />
                  <Line type="monotone" dataKey="pitching" stroke="#ff7849" strokeWidth={2} />
                </LineChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>

          {/* Top Performers */}
          <Card className="floating-card">
            <CardHeader>
              <CardTitle className="text-white">Top Performers</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {facilityStats.topPerformers.map((performer, index) => (
                  <div key={index} className="flex items-center justify-between p-3 bg-gray-800 rounded-lg">
                    <div className="flex items-center space-x-3">
                      <div className="w-8 h-8 bg-orange-500 rounded-full flex items-center justify-center text-white font-bold">
                        {index + 1}
                      </div>
                      <div>
                        <p className="font-medium text-white">{performer.name}</p>
                        <p className="text-sm text-gray-400 capitalize">{performer.type}</p>
                      </div>
                    </div>
                    <div className="text-right">
                      <p className="font-bold text-white">{performer.score}</p>
                      <p className="text-xs text-gray-400">
                        {performer.type === 'hitting' ? 'mph' : 'mph'}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Quick Actions */}
        <Card className="floating-card">
          <CardHeader>
            <CardTitle className="text-white">Quick Actions</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <Button className="btn-primary h-16 flex flex-col items-center justify-center space-y-2">
                <Upload className="h-6 w-6" />
                <span>Upload Data</span>
              </Button>
              <Button variant="outline" className="h-16 flex flex-col items-center justify-center space-y-2 border-orange-500 text-orange-500 hover:bg-orange-500 hover:text-white">
                <Users className="h-6 w-6" />
                <span>Manage Players</span>
              </Button>
              <Button variant="outline" className="h-16 flex flex-col items-center justify-center space-y-2 border-orange-500 text-orange-500 hover:bg-orange-500 hover:text-white">
                <TrendingUp className="h-6 w-6" />
                <span>View Reports</span>
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </MainLayout>
  );
}
