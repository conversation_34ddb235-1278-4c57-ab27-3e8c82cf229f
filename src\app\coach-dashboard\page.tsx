"use client";

import React, { useState, useEffect } from 'react';
import { <PERSON>, CardHeader, CardT<PERSON>le, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, LineChart, Line } from 'recharts';
import { UserCheck, TrendingUp, Target, Award, Users, Calendar } from 'lucide-react';
import MainLayout from '@/components/layout/MainLayout';
import { getStorageData, STORAGE_KEYS } from '@/lib/storage';

interface CoachImpactMetrics {
  totalPlayers: number;
  avgImprovement: number;
  sessionsThisMonth: number;
  successRate: number;
  topImprovements: Array<{
    player: string;
    metric: string;
    improvement: number;
    timeframe: string;
  }>;
}

export default function CoachDashboard() {
  const [selectedCoach, setSelectedCoach] = useState<string>('All Coaches');
  const [timeframe, setTimeframe] = useState<'week' | 'month' | 'quarter'>('month');
  const [coachMetrics, setCoachMetrics] = useState<CoachImpactMetrics>({
    totalPlayers: 0,
    avgImprovement: 0,
    sessionsThisMonth: 0,
    successRate: 0,
    topImprovements: []
  });

  useEffect(() => {
    calculateCoachMetrics();
  }, [selectedCoach, timeframe]);

  const calculateCoachMetrics = () => {
    // Load data from localStorage
    const hittingData = getStorageData(STORAGE_KEYS.HITTING_DATA, []);
    const pitchingData = getStorageData(STORAGE_KEYS.PITCHING_DATA, []);
    
    // Calculate metrics (simplified for demo)
    const allPlayers = new Set([
      ...hittingData.map((h: any) => h.Batter),
      ...pitchingData.map((p: any) => p.Pitcher)
    ]);
    
    const topImprovements = [
      {
        player: 'John Smith',
        metric: 'Exit Velocity',
        improvement: 8.5,
        timeframe: 'Last Month'
      },
      {
        player: 'Mike Johnson',
        metric: 'Launch Angle',
        improvement: 12.3,
        timeframe: 'Last Month'
      },
      {
        player: 'Sarah Davis',
        metric: 'Pitch Velocity',
        improvement: 5.2,
        timeframe: 'Last Month'
      }
    ];
    
    setCoachMetrics({
      totalPlayers: allPlayers.size,
      avgImprovement: 7.8,
      sessionsThisMonth: 24,
      successRate: 85,
      topImprovements
    });
  };

  // Sample data for charts
  const improvementTrend = [
    { week: 'Week 1', hitting: 5.2, pitching: 4.8 },
    { week: 'Week 2', hitting: 6.1, pitching: 5.5 },
    { week: 'Week 3', hitting: 7.3, pitching: 6.2 },
    { week: 'Week 4', hitting: 8.1, pitching: 7.1 },
  ];

  const playerProgress = [
    { name: 'John S.', before: 85, after: 93 },
    { name: 'Mike J.', before: 78, after: 87 },
    { name: 'Sarah D.', before: 82, after: 89 },
    { name: 'Tom W.', before: 76, after: 84 },
    { name: 'Lisa M.', before: 88, after: 94 },
  ];

  return (
    <MainLayout>
      <div className="p-6 space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-white">Coach Dashboard</h1>
            <p className="text-gray-400 mt-2">Track coaching effectiveness and player development</p>
          </div>
          <div className="flex items-center space-x-4">
            <Select value={timeframe} onValueChange={(value: any) => setTimeframe(value)}>
              <SelectTrigger className="w-32 bg-gray-800 border-gray-600 text-white">
                <SelectValue />
              </SelectTrigger>
              <SelectContent className="bg-gray-800 border-gray-600">
                <SelectItem value="week">Week</SelectItem>
                <SelectItem value="month">Month</SelectItem>
                <SelectItem value="quarter">Quarter</SelectItem>
              </SelectContent>
            </Select>
            <Select value={selectedCoach} onValueChange={setSelectedCoach}>
              <SelectTrigger className="w-48 bg-gray-800 border-gray-600 text-white">
                <SelectValue />
              </SelectTrigger>
              <SelectContent className="bg-gray-800 border-gray-600">
                <SelectItem value="All Coaches">All Coaches</SelectItem>
                <SelectItem value="Coach Smith">Coach Smith</SelectItem>
                <SelectItem value="Coach Johnson">Coach Johnson</SelectItem>
                <SelectItem value="Coach Davis">Coach Davis</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        {/* Metrics Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <Card className="floating-card">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-400">Players Coached</p>
                  <p className="text-3xl font-bold text-white">{coachMetrics.totalPlayers}</p>
                </div>
                <Users className="h-8 w-8 text-orange-500" />
              </div>
            </CardContent>
          </Card>

          <Card className="floating-card">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-400">Avg Improvement</p>
                  <p className="text-3xl font-bold text-white">{coachMetrics.avgImprovement}%</p>
                </div>
                <TrendingUp className="h-8 w-8 text-orange-500" />
              </div>
            </CardContent>
          </Card>

          <Card className="floating-card">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-400">Sessions This Month</p>
                  <p className="text-3xl font-bold text-white">{coachMetrics.sessionsThisMonth}</p>
                </div>
                <Calendar className="h-8 w-8 text-orange-500" />
              </div>
            </CardContent>
          </Card>

          <Card className="floating-card">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-400">Success Rate</p>
                  <p className="text-3xl font-bold text-white">{coachMetrics.successRate}%</p>
                </div>
                <Award className="h-8 w-8 text-orange-500" />
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Charts */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Improvement Trends */}
          <Card className="floating-card">
            <CardHeader>
              <CardTitle className="text-white">Weekly Improvement Trends</CardTitle>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={300}>
                <LineChart data={improvementTrend}>
                  <CartesianGrid strokeDasharray="3 3" stroke="rgba(255,255,255,0.1)" />
                  <XAxis dataKey="week" stroke="#ffffff" />
                  <YAxis stroke="#ffffff" />
                  <Tooltip 
                    contentStyle={{ 
                      backgroundColor: '#1a1a2e', 
                      border: '1px solid #e55a2b',
                      borderRadius: '8px',
                      color: '#ffffff'
                    }} 
                  />
                  <Line type="monotone" dataKey="hitting" stroke="#e55a2b" strokeWidth={2} />
                  <Line type="monotone" dataKey="pitching" stroke="#ff7849" strokeWidth={2} />
                </LineChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>

          {/* Player Progress */}
          <Card className="floating-card">
            <CardHeader>
              <CardTitle className="text-white">Player Progress Comparison</CardTitle>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={300}>
                <BarChart data={playerProgress}>
                  <CartesianGrid strokeDasharray="3 3" stroke="rgba(255,255,255,0.1)" />
                  <XAxis dataKey="name" stroke="#ffffff" />
                  <YAxis stroke="#ffffff" />
                  <Tooltip 
                    contentStyle={{ 
                      backgroundColor: '#1a1a2e', 
                      border: '1px solid #e55a2b',
                      borderRadius: '8px',
                      color: '#ffffff'
                    }} 
                  />
                  <Bar dataKey="before" fill="#6b7280" name="Before" />
                  <Bar dataKey="after" fill="#e55a2b" name="After" />
                </BarChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </div>

        {/* Top Improvements */}
        <Card className="floating-card">
          <CardHeader>
            <CardTitle className="text-white">Top Player Improvements</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {coachMetrics.topImprovements.map((improvement, index) => (
                <div key={index} className="flex items-center justify-between p-4 bg-gray-800 rounded-lg">
                  <div className="flex items-center space-x-4">
                    <div className="w-10 h-10 bg-orange-500 rounded-full flex items-center justify-center text-white font-bold">
                      {index + 1}
                    </div>
                    <div>
                      <p className="font-medium text-white">{improvement.player}</p>
                      <p className="text-sm text-gray-400">{improvement.metric}</p>
                    </div>
                  </div>
                  <div className="text-right">
                    <p className="text-lg font-bold text-green-400">+{improvement.improvement}%</p>
                    <p className="text-xs text-gray-400">{improvement.timeframe}</p>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Action Items */}
        <Card className="floating-card">
          <CardHeader>
            <CardTitle className="text-white">Coaching Action Items</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="p-4 bg-yellow-900/20 border border-yellow-600 rounded-lg">
                <h3 className="font-semibold text-yellow-400 mb-2">Focus Areas</h3>
                <ul className="text-sm text-gray-300 space-y-1">
                  <li>• Launch angle consistency for 3 players</li>
                  <li>• Pitch velocity development program</li>
                  <li>• Contact quality improvement drills</li>
                </ul>
              </div>
              <div className="p-4 bg-green-900/20 border border-green-600 rounded-lg">
                <h3 className="font-semibold text-green-400 mb-2">Recent Successes</h3>
                <ul className="text-sm text-gray-300 space-y-1">
                  <li>• John Smith: 8.5% exit velocity increase</li>
                  <li>• Sarah Davis: Consistent 90+ mph fastball</li>
                  <li>• Team average improvement: 7.8%</li>
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </MainLayout>
  );
}
