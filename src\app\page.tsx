"use client"

import { useState, useEffect } from 'react';
console.log('Page component rendering');
import HittingMetricsDashboard from '@/components/dashboard/Hitting/HittingMetricsDashboard';
import PitchingMetricsDashboard from '@/components/dashboard/PitchingMetricsDashboard';
import { Button } from '@/components/ui/button';
import { CircleDot, Target } from 'lucide-react';
import { useSearchParams } from 'next/navigation';

export default function Home() {
  const [activeDashboard, setActiveDashboard] = useState<'pitching' | 'hitting'>('hitting');
  const searchParams = useSearchParams();

  useEffect(() => {
    const view = searchParams.get('view');
    if (view === 'hitting') {
      setActiveDashboard('hitting');
    }
  }, [searchParams]);  

  return (
    <main className="min-h-screen p-4 relative">
      {/* Dashboard Toggle */}
      <div className="fixed top-4 right-4 z-50 flex gap-2">
        <Button
          variant={activeDashboard === 'pitching' ? 'default' : 'outline'}
          onClick={() => setActiveDashboard('pitching')}
          className="flex items-center gap-2 bg-white/80 text-black border-black/20 hover:bg-white/90"
        >
          <CircleDot className="w-4 h-4" />
          Pitching
        </Button>
        <Button
          variant={activeDashboard === 'hitting' ? 'default' : 'outline'}
          onClick={() => setActiveDashboard('hitting')}
          className="flex items-center gap-2 bg-white/80 text-black border-black/20 hover:bg-white/90"
        >
          <Target className="w-4 h-4" />
          Hitting
        </Button>
      </div>

      {/* Dashboards */}
      {activeDashboard === 'pitching' ? (
        <PitchingMetricsDashboard />
      ) : (
        <HittingMetricsDashboard />
      )}
    </main>
  );
}