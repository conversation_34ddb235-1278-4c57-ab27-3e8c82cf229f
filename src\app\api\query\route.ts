import { NextRequest, NextResponse } from 'next/server';
import { buildPrompt, validateMetricsForQuery } from '@/server/ai/llamaPrompts';
import { getPlayerMetrics } from '@/server/data';
import { QueryRequest, AIResponse, LLaMARequest, LLaMAResponse } from '@/types/ai';
import { HittingData } from '@/components/dashboard/Hitting/HittingMetricsDashboard';
import { PitchData } from '@/components/dashboard/PitchingMetricsDashboard';

const LLAMA_URL = process.env.LLAMA_API_URL!;
const TIMEOUT = parseInt(process.env.LLAMA_TIMEOUT_MS!) || 10000;
const MAX_TOKENS = parseInt(process.env.LLAMA_MAX_TOKENS!) || 200;

// Extended QueryRequest to include data
interface ExtendedQueryRequest extends QueryRequest {
  hittingData?: HittingData[];
  pitchingData?: PitchData[];
  level?: 'Professional' | 'College' | 'High School';
}

export async function POST(request: NextRequest) {
  try {
    const body: ExtendedQueryRequest = await request.json();
    const { playerId, question, dataType, hittingData = [], pitchingData = [], level = 'Professional' } = body;

    if (!playerId || !question) {
      return NextResponse.json(
        { error: 'Missing required fields: playerId and question' },
        { status: 400 }
      );
    }

    // Get player metrics from the provided data
    const metrics = getPlayerMetrics(playerId, hittingData, pitchingData, level);

    // Check if we have any data for the player
    if (!metrics.hitting && !metrics.pitching) {
      return NextResponse.json({
        text: `I don't have any performance data for ${playerId}. Please upload hitting or pitching data first, then select a player who appears in that data.`,
        error: 'No data found for player'
      } as AIResponse);
    }

    // Validate that we have the right data for the question
    if (!validateMetricsForQuery(metrics, question)) {
      return NextResponse.json({
        text: `I don't have ${dataType || 'the requested'} data for ${playerId}. Please make sure the player has recorded ${dataType || 'performance'} data.`,
        error: 'Insufficient data for query'
      } as AIResponse);
    }

    // Build the prompt for LLaMA
    const prompt = buildPrompt(metrics, question);

    // Call LLaMA API
    const llamaRequest: LLaMARequest = {
      prompt,
      max_tokens: MAX_TOKENS,
      temperature: 0.7,
      top_p: 0.9,
      stop: ['Human:', 'User:', '\n\nUser:', '\n\nHuman:']
    };

    // console.log('Calling LLaMA with prompt:', prompt.substring(0, 200) + '...');

    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), TIMEOUT);

    try {
      const response = await fetch(LLAMA_URL, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(llamaRequest),
        signal: controller.signal
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        throw new Error(`LLaMA API error: ${response.status} ${response.statusText}`);
      }

      const llamaResponse: LLaMAResponse = await response.json();
      // console.log('LLaMA response:', llamaResponse);

      // Extract the generated text
      const generatedText = llamaResponse.content || '';

      // Try to parse as JSON
      let aiResponse: AIResponse;
      try {
        // Clean up the response - remove any markdown formatting
        const cleanedText = generatedText
          .replace(/```json\s*/g, '')
          .replace(/```\s*/g, '')
          .trim();

        const parsed = JSON.parse(cleanedText);
        
        // Validate the structure
        if (typeof parsed.text === 'string') {
          aiResponse = {
            text: parsed.text,
            ...(parsed.chart && { chart: parsed.chart })
          };
        } else {
          throw new Error('Invalid response structure');
        }
      } catch (parseError) {
        console.error('Failed to parse LLaMA response as JSON:', parseError);
        console.error('Raw response:', generatedText);
        
        // Fallback: return the raw text
        aiResponse = {
          text: generatedText || 'I apologize, but I encountered an error processing your question. Please try rephrasing it.',
          error: 'Failed to parse AI response'
        };
      }

      return NextResponse.json(aiResponse);

    } catch (fetchError) {
      clearTimeout(timeoutId);
      
      if (fetchError instanceof Error && fetchError.name === 'AbortError') {
        return NextResponse.json(
          { 
            text: 'The request timed out. Please try again with a simpler question.',
            error: 'Request timeout' 
          } as AIResponse,
          { status: 408 }
        );
      }

      console.error('LLaMA API call failed:', fetchError);
      return NextResponse.json(
        { 
          text: 'I\'m having trouble connecting to the AI service. Please try again later.',
          error: 'LLaMA service unavailable' 
        } as AIResponse,
        { status: 502 }
      );
    }

  } catch (error) {
    console.error('API route error:', error);
    return NextResponse.json(
      { 
        text: 'An unexpected error occurred. Please try again.',
        error: 'Internal server error' 
      } as AIResponse,
      { status: 500 }
    );
  }
}
