@echo off
echo.
echo ========================================
echo   BLP 3.0 - NUCLEAR FRESH START
echo ========================================
echo.

echo 🔪 Step 1: Killing any running Next.js processes...
taskkill /f /im node.exe >nul 2>&1
for /f "tokens=5" %%a in ('netstat -ano ^| findstr :3000') do taskkill /f /pid %%a >nul 2>&1
echo ✅ Processes killed

echo.
echo 🧹 Step 2: Clearing all caches (aggressive mode)...
call npm run clear-cache

echo.
echo ⏳ Step 3: Waiting for file system to release locks...
timeout /t 3 /nobreak >nul

echo.
echo 🚀 Step 4: Starting development server...
echo.
echo 💡 The app will start with completely fresh cache
echo 💡 Use the debug panel (🐛 button) if you encounter issues
echo 💡 If it still doesn't work, close your IDE and try again
echo.

call npm run dev

echo.
echo Press any key to exit...
pause >nul
