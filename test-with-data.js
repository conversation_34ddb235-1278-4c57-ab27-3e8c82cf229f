// Test with actual hitting data
const testWithData = async () => {
  try {
    console.log('Testing with real hitting data...');
    
    const testData = {
      playerId: '<PERSON>',
      question: 'Analyze my performance scores.',
      dataType: 'hitting',
      level: 'Professional',
      hittingData: [
        {
          Batter: '<PERSON>',
          BatterSide: 'R',
          ExitSpeed: 95.2,
          RelSpeed: 88.5,
          Angle: 15.3,
          Direction: 12.5,
          Distance: 320,
          HitSpinRate: 2200,
          ContactPositionY: 0.5,
          HitSpinAxis: 180,
          Level: 'Professional'
        },
        {
          Batter: '<PERSON>',
          BatterSide: 'R',
          ExitSpeed: 89.8,
          RelSpeed: 91.2,
          Angle: 22.1,
          Direction: -8.3,
          Distance: 285,
          HitSpinRate: 2450,
          ContactPositionY: 0.3,
          HitSpinAxis: 165,
          Level: 'Professional'
        },
        {
          Batter: '<PERSON>',
          BatterSide: 'R',
          ExitSpeed: 102.1,
          RelSpeed: 89.7,
          Angle: 18.5,
          Direction: 5.2,
          Distance: 385,
          HitSpinRate: 2100,
          ContactPositionY: 0.4,
          HitSpinAxis: 175,
          Level: 'Professional'
        }
      ],
      pitchingData: []
    };

    console.log('Sending request to API...');
    const response = await fetch('http://localhost:3000/api/query', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(testData),
    });

    console.log('Response status:', response.status);
    
    if (!response.ok) {
      const errorText = await response.text();
      console.log('Error response:', errorText);
      return;
    }

    const result = await response.json();
    console.log('✅ Full integration working!');
    console.log('AI Response:', result.text);
    
    if (result.chart) {
      console.log('📊 Chart data included:', result.chart);
    }
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
};

testWithData();
