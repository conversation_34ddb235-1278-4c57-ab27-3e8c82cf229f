/**
 * Storage utility for managing localStorage with cache busting and validation
 */

// Version key for cache busting
const STORAGE_VERSION = '1.0.0';
const VERSION_KEY = 'blp_storage_version';

// Storage keys
export const STORAGE_KEYS = {
  HITTING_DATA: 'hittingData',
  PITCHING_DATA: 'pitchingData',
  SELECTED_BATTER: 'selectedBatter',
  SELECTED_PITCHER: 'selectedPitcher',
  SELECTED_LEVEL: 'selectedLevel',
  REPORT_TEMPLATE: 'reportTemplate',
} as const;

/**
 * Check if localStorage is available
 */
function isLocalStorageAvailable(): boolean {
  try {
    const test = '__localStorage_test__';
    localStorage.setItem(test, test);
    localStorage.removeItem(test);
    return true;
  } catch {
    return false;
  }
}

/**
 * Check if stored data version matches current version
 */
function isStorageVersionValid(): boolean {
  if (!isLocalStorageAvailable()) return false;
  
  try {
    const storedVersion = localStorage.getItem(VERSION_KEY);
    return storedVersion === STORAGE_VERSION;
  } catch {
    return false;
  }
}

/**
 * Clear all application data from localStorage
 */
export function clearAllStorageData(): void {
  if (!isLocalStorageAvailable()) return;
  
  try {
    Object.values(STORAGE_KEYS).forEach(key => {
      localStorage.removeItem(key);
    });
    localStorage.removeItem(VERSION_KEY);
    console.log('All storage data cleared');
  } catch (error) {
    console.error('Error clearing storage data:', error);
  }
}

/**
 * Initialize storage with version check
 */
export function initializeStorage(): void {
  if (!isLocalStorageAvailable()) {
    console.warn('localStorage is not available');
    return;
  }
  
  if (!isStorageVersionValid()) {
    console.log('Storage version mismatch or missing, clearing old data');
    clearAllStorageData();
    localStorage.setItem(VERSION_KEY, STORAGE_VERSION);
  }
}

/**
 * Safely get data from localStorage with validation
 */
export function getStorageData<T>(key: string, defaultValue: T): T {
  if (!isLocalStorageAvailable()) return defaultValue;
  
  try {
    const item = localStorage.getItem(key);
    if (item === null) return defaultValue;
    
    const parsed = JSON.parse(item);
    return parsed !== null ? parsed : defaultValue;
  } catch (error) {
    console.error(`Error reading from localStorage key "${key}":`, error);
    return defaultValue;
  }
}

/**
 * Safely set data to localStorage
 */
export function setStorageData<T>(key: string, value: T): boolean {
  if (!isLocalStorageAvailable()) return false;
  
  try {
    localStorage.setItem(key, JSON.stringify(value));
    return true;
  } catch (error) {
    console.error(`Error writing to localStorage key "${key}":`, error);
    return false;
  }
}

/**
 * Remove specific item from localStorage
 */
export function removeStorageData(key: string): void {
  if (!isLocalStorageAvailable()) return;
  
  try {
    localStorage.removeItem(key);
  } catch (error) {
    console.error(`Error removing localStorage key "${key}":`, error);
  }
}

/**
 * Get all stored data for debugging
 */
export function getAllStorageData(): Record<string, any> {
  if (!isLocalStorageAvailable()) return {};
  
  const data: Record<string, any> = {};
  
  Object.values(STORAGE_KEYS).forEach(key => {
    try {
      const item = localStorage.getItem(key);
      if (item !== null) {
        data[key] = JSON.parse(item);
      }
    } catch (error) {
      console.error(`Error reading key "${key}":`, error);
      data[key] = null;
    }
  });
  
  return data;
}

/**
 * Validate hitting data structure
 */
export function validateHittingData(data: any): boolean {
  if (!Array.isArray(data)) return false;
  
  // Check if at least one item has required fields
  return data.length === 0 || data.some(item => 
    item && 
    typeof item === 'object' && 
    'Batter' in item
  );
}

/**
 * Validate pitching data structure
 */
export function validatePitchingData(data: any): boolean {
  if (!Array.isArray(data)) return false;
  
  // Check if at least one item has required fields
  return data.length === 0 || data.some(item => 
    item && 
    typeof item === 'object' && 
    'Pitcher' in item
  );
}

/**
 * Development mode: Clear cache on hot reload
 */
export function clearCacheInDevelopment(): void {
  if (process.env.NODE_ENV === 'development') {
    // More aggressive cache clearing in development
    const sessionKey = 'blp_dev_session';
    const buildKey = 'blp_build_hash';
    const currentSession = Date.now().toString();
    const lastSession = localStorage.getItem(sessionKey);

    // Create a simple build hash based on current time (changes on each server restart)
    const currentBuildHash = Math.floor(Date.now() / (1000 * 60)).toString(); // Changes every minute
    const lastBuildHash = localStorage.getItem(buildKey);

    // Clear cache if:
    // 1. No previous session
    // 2. More than 2 minutes since last session
    // 3. Build hash changed (server restart)
    const shouldClear = !lastSession ||
                       (Date.now() - parseInt(lastSession)) > 2 * 60 * 1000 ||
                       lastBuildHash !== currentBuildHash;

    if (shouldClear) {
      console.log('Development mode: Clearing stale cache (aggressive mode)');
      clearAllStorageData();
      localStorage.setItem(sessionKey, currentSession);
      localStorage.setItem(buildKey, currentBuildHash);
      localStorage.setItem(VERSION_KEY, STORAGE_VERSION);
    }
  }
}

/**
 * Force clear all caches (for debugging)
 */
export function forceClearAllCaches(): void {
  console.log('Force clearing all caches...');

  // Clear localStorage
  clearAllStorageData();

  // Clear sessionStorage
  try {
    sessionStorage.clear();
  } catch (error) {
    console.warn('Could not clear sessionStorage:', error);
  }

  // Clear any other browser caches we can access
  try {
    if ('caches' in window) {
      caches.keys().then(names => {
        names.forEach(name => {
          caches.delete(name);
        });
      });
    }
  } catch (error) {
    console.warn('Could not clear cache API:', error);
  }

  console.log('All caches cleared. Reloading page...');
  window.location.reload();
}
