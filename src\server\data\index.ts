import { PlayerMetrics } from '@/types/ai';
import { HittingData } from '@/components/dashboard/Hitting/HittingMetricsDashboard';
import {
  calculateQualityOfContactScore,
  calculateLaunchAngleScore,
  calculateRoomForErrorScore,
  calculateTotalHitScore,
  calculateAveragePitch,
  calculateArsenalScore,
  LEVEL_BENCHMARKS,
  DEFAULT_PARAMS,
  PitchData
} from '@/lib/calculations';

/**
 * Extract player metrics from hitting data
 */
export const extractHittingMetrics = (
  playerId: string,
  hittingData: HittingData[],
  level: 'Professional' | 'College' | 'High School'
) => {
  const playerHits = hittingData.filter(hit => hit.Batter === playerId);
  
  if (playerHits.length === 0) {
    return null;
  }

  // Calculate basic stats
  const totalHits = playerHits.length;
  const avgExitVelocity = playerHits.reduce((sum, hit) => sum + hit.ExitSpeed, 0) / totalHits;
  const avgLaunchAngle = playerHits.reduce((sum, hit) => sum + hit.Angle, 0) / totalHits;
  const avgDistance = playerHits.reduce((sum, hit) => sum + hit.Distance, 0) / totalHits;

  // Calculate scores using existing functions
  const benchmarks = LEVEL_BENCHMARKS[level];
  const qualityOfContactScore = calculateQualityOfContactScore(playerHits, benchmarks, level);

  // For launch angle, use aggregated hit data
  const avgExitSpeed = playerHits.reduce((sum, hit) => sum + hit.ExitSpeed, 0) / totalHits;
  const avgAngle = playerHits.reduce((sum, hit) => sum + hit.Angle, 0) / totalHits;
  const avgSpinRate = playerHits.reduce((sum, hit) => sum + (hit.HitSpinRate || 0), 0) / totalHits;
  const aggregatedHit = {
    ...playerHits[0],
    ExitSpeed: avgExitSpeed,
    Angle: avgAngle,
    HitSpinRate: avgSpinRate,
    Level: level
  };

  const launchAngleScore = calculateLaunchAngleScore(aggregatedHit, DEFAULT_PARAMS.launchAngle, level);
  const roomForErrorScore = calculateRoomForErrorScore(playerHits, totalHits, DEFAULT_PARAMS.roomForError, level);
  const totalScore = calculateTotalHitScore(playerHits, totalHits, playerHits, level);

  // Group hits by velocity ranges
  const velocityRanges = [
    { min: 0, max: 80, label: 'Under 80 mph' },
    { min: 80, max: 90, label: '80-90 mph' },
    { min: 90, max: 100, label: '90-100 mph' },
    { min: 100, max: 110, label: '100-110 mph' },
    { min: 110, max: 999, label: 'Over 110 mph' }
  ];

  const hitsByVelocityRange = velocityRanges.map(range => {
    const hitsInRange = playerHits.filter(hit => 
      hit.ExitSpeed >= range.min && hit.ExitSpeed < range.max
    );
    return {
      range: range.label,
      count: hitsInRange.length,
      avgExitVelo: hitsInRange.length > 0 
        ? hitsInRange.reduce((sum, hit) => sum + hit.ExitSpeed, 0) / hitsInRange.length 
        : 0
    };
  }).filter(range => range.count > 0);

  return {
    totalHits,
    avgExitVelocity: Math.round(avgExitVelocity * 100) / 100,
    avgLaunchAngle: Math.round(avgLaunchAngle * 100) / 100,
    avgDistance: Math.round(avgDistance * 100) / 100,
    qualityOfContactScore: Math.round(qualityOfContactScore * 100) / 100,
    launchAngleScore: Math.round(launchAngleScore * 100) / 100,
    roomForErrorScore: Math.round(roomForErrorScore * 100) / 100,
    totalScore: Math.round(totalScore * 100) / 100,
    hitsByVelocityRange
  };
};

/**
 * Extract player metrics from pitching data
 */
export const extractPitchingMetrics = (
  playerId: string,
  pitchingData: PitchData[],
  level: 'Professional' | 'College' | 'High School'
) => {
  const playerPitches = pitchingData.filter(pitch => pitch.Pitcher === playerId);
  
  if (playerPitches.length === 0) {
    return null;
  }

  const totalPitches = playerPitches.length;
  const arsenalScore = calculateArsenalScore(playerPitches);

  // Group by pitch types
  const pitchTypes = [...new Set(playerPitches.map(p => p.AutoPitchType))];
  const pitchTypeMetrics = pitchTypes.map(type => {
    const pitchesOfType = playerPitches.filter(p => p.AutoPitchType === type);
    const avgPitch = calculateAveragePitch(pitchesOfType);

    return {
      type,
      count: pitchesOfType.length,
      avgVelocity: Math.round(avgPitch.RelSpeed * 100) / 100,
      avgSpinRate: Math.round(avgPitch.SpinRate * 100) / 100,
      avgVertBreak: Math.round(avgPitch.VertBreak * 100) / 100,
      avgHorzBreak: Math.round(avgPitch.HorzBreak * 100) / 100,
      score: 75 // Simplified score for now
    };
  });

  // Velocity analysis by pitch type
  const velocityByPitchType = pitchTypes.map(type => {
    const pitchesOfType = playerPitches.filter(p => p.AutoPitchType === type);
    const velocities = pitchesOfType.map(p => p.RelSpeed);
    
    return {
      pitchType: type,
      avgVelocity: Math.round((velocities.reduce((sum, v) => sum + v, 0) / velocities.length) * 100) / 100,
      maxVelocity: Math.round(Math.max(...velocities) * 100) / 100,
      minVelocity: Math.round(Math.min(...velocities) * 100) / 100
    };
  });

  return {
    totalPitches,
    arsenalScore: Math.round(arsenalScore * 100) / 100,
    pitchTypes: pitchTypeMetrics,
    velocityByPitchType
  };
};

/**
 * Get comprehensive player metrics for AI queries
 */
export const getPlayerMetrics = (
  playerId: string,
  hittingData: HittingData[] = [],
  pitchingData: PitchData[] = [],
  level: 'Professional' | 'College' | 'High School' = 'Professional'
): PlayerMetrics => {
  // Find player name from either dataset
  const playerName = 
    hittingData.find(h => h.Batter === playerId)?.Batter ||
    pitchingData.find(p => p.Pitcher === playerId)?.Pitcher ||
    playerId;

  const hitting = extractHittingMetrics(playerId, hittingData, level);
  const pitching = extractPitchingMetrics(playerId, pitchingData, level);

  let dataType: 'hitting' | 'pitching' | 'both' = 'both';
  if (hitting && !pitching) dataType = 'hitting';
  if (pitching && !hitting) dataType = 'pitching';

  return {
    playerId,
    playerName,
    level,
    dataType,
    ...(hitting && { hitting }),
    ...(pitching && { pitching })
  };
};
