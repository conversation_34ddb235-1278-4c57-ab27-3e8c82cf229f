"use client"

import React from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '../../../components/ui/badge'; // Make sure you have this component
import { X } from 'lucide-react'; // For remove icons
import { VelocityRange } from './HittingMetricsDashboard';

// Theme constants to match the main dashboard
const THEME_COLORS = {
  primary: '#e55a2b', // Softer burnt orange
  cardBackground: 'rgba(15, 15, 35, 0.8)', // Dark semi-transparent
  cardBorder: '#e55a2b', // Softer burnt orange border
  text: '#ffffff',
  textSecondary: '#a0a0a0',
  glowColor: '#e55a2b'
} as const;

interface VelocityFilterProps {
  exitRanges: VelocityRange[];
  pitchRanges: VelocityRange[];
  selectedExitRanges: VelocityRange[];
  selectedPitchRanges: VelocityRange[];
  onExitRangeChange: (ranges: VelocityRange[]) => void;
  onPitchRangeChange: (ranges: VelocityRange[]) => void;
}

const VelocityFilter: React.FC<VelocityFilterProps> = ({
  exitRanges,
  pitchRanges,
  selectedExitRanges,
  selectedPitchRanges,
  onExitRangeChange,
  onPitchRangeChange,
}) => {
  return (
    <Card className={`bg-[${THEME_COLORS.cardBackground}] backdrop-blur-sm border-0 rounded-lg transform translate-y-0 shadow-[0_4px_16px_rgba(229,90,43,0.15)] hover:shadow-[0_6px_24px_rgba(229,90,43,0.25)] hover:-translate-y-1 transition-all duration-300`}>
      <CardContent className="p-4 grid grid-cols-2 gap-4">
        {/* Exit Velocity Filter */}
        <div>
          <h3 className="font-semibold mb-2 text-white">Exit Velocity</h3>
          <div className="flex flex-wrap gap-2 mb-2">
            {selectedExitRanges.map((range) => (
              <Badge
                key={range.label}
                className="bg-[#e55a2b]/20 text-[#e55a2b] border border-[#e55a2b]/50 hover:bg-[#e55a2b]/30 transition-colors"
              >
                {range.label}
                <button
                  onClick={() => onExitRangeChange(selectedExitRanges.filter(r => r.label !== range.label))}
                  className="ml-1 hover:text-[#ff8c5a] transition-colors"
                >
                  <X size={14} />
                </button>
              </Badge>
            ))}
          </div>
          <Select
            onValueChange={(value) => {
              const selectedRange = exitRanges.find(range => range.label === value);
              if (selectedRange) {
                if (selectedRange.label === 'All Exit Velos') {
                  onExitRangeChange([selectedRange]);
                } else {
                  // Remove "All" if it's selected and add the new range
                  const newRanges = selectedExitRanges
                    .filter(r => r.label !== 'All Exit Velos')
                    .concat(selectedRange);
                  onExitRangeChange(newRanges);
                }
              }
            }}
          >
            <SelectTrigger className={`w-full bg-[${THEME_COLORS.cardBackground}] text-white border border-[${THEME_COLORS.cardBorder}] shadow-lg shadow-[${THEME_COLORS.glowColor}]/20`}>
              <SelectValue placeholder="Add Exit Velocity Range" />
            </SelectTrigger>
            <SelectContent className={`bg-[${THEME_COLORS.cardBackground}] text-white border border-[${THEME_COLORS.cardBorder}] shadow-lg`}>
              {exitRanges.map((range) => (
                <SelectItem key={range.label} value={range.label}>
                  {range.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {/* Pitch Velocity Filter */}
        <div>
          <h3 className="font-semibold mb-2 text-white">Pitch Velocity</h3>
          <div className="flex flex-wrap gap-2 mb-2">
            {selectedPitchRanges.map((range) => (
              <Badge
                key={range.label}
                className="bg-[#e55a2b]/20 text-[#e55a2b] border border-[#e55a2b]/50 hover:bg-[#e55a2b]/30 transition-colors"
              >
                {range.label}
                <button
                  onClick={() => onPitchRangeChange(selectedPitchRanges.filter(r => r.label !== range.label))}
                  className="ml-1 hover:text-[#ff8c5a] transition-colors"
                >
                  <X size={14} />
                </button>
              </Badge>
            ))}
          </div>
          <Select
            onValueChange={(value) => {
              const selectedRange = pitchRanges.find(range => range.label === value);
              if (selectedRange) {
                if (selectedRange.label === 'All Pitch Velos') {
                  onPitchRangeChange([selectedRange]);
                } else {
                  // Remove "All" if it's selected and add the new range
                  const newRanges = selectedPitchRanges
                    .filter(r => r.label !== 'All Pitch Velos')
                    .concat(selectedRange);
                  onPitchRangeChange(newRanges);
                }
              }
            }}
          >
            <SelectTrigger className={`w-full bg-[${THEME_COLORS.cardBackground}] text-white border border-[${THEME_COLORS.cardBorder}] shadow-lg shadow-[${THEME_COLORS.glowColor}]/20`}>
              <SelectValue placeholder="Add Pitch Velocity Range" />
            </SelectTrigger>
            <SelectContent className={`bg-[${THEME_COLORS.cardBackground}] text-white border border-[${THEME_COLORS.cardBorder}] shadow-lg`}>
              {pitchRanges.map((range) => (
                <SelectItem key={range.label} value={range.label}>
                  {range.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      </CardContent>
    </Card>
  );
};

export default VelocityFilter;