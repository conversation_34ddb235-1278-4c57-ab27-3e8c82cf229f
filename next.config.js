/** @type {import('next').NextConfig} */
const nextConfig = {
  reactStrictMode: true,

  // Completely disable caching in development
  ...(process.env.NODE_ENV === 'development' && {
    // Disable webpack caching
    webpack: (config, { dev, isServer }) => {
      if (dev) {
        config.cache = false;
      }
      return config;
    },

    // Disable on-demand entries caching
    onDemandEntries: {
      maxInactiveAge: 0,
      pagesBufferLength: 0,
    },

    // Disable experimental features that cache
    experimental: {
      isrMemoryCacheSize: 0,
      workerThreads: false,
      cpus: 1,
    },
  }),

  // Disable ETags and other caching mechanisms
  generateEtags: false,
  poweredByHeader: false,

  // Force fresh builds
  ...(process.env.NODE_ENV === 'development' && {
    distDir: '.next',
  }),
};

module.exports = nextConfig;

