import React, { useState } from 'react';

interface SprayChartProps {
  data: Array<{
    Batter: string;
    Direction: number;
    Distance: number;
    ExitSpeed: number;
    Angle: number;
    'Event Date': string;
  }>;
  selectedBatter: string;
}

// MLB Field Dimensions (in feet) - Accurate to real baseball fields
const FIELD_DIMENSIONS = {
  // Infield
  homeToFirst: 90,
  homeToSecond: 127.28, // sqrt(90^2 + 90^2)
  homeToThird: 90,
  pitcherMound: 60.5,

  // Outfield walls (typical MLB dimensions)
  leftField: 330,
  leftCenter: 375,
  centerField: 400,
  rightCenter: 375,
  rightField: 330,

  // Foul territory
  foulTerritoryDepth: 60,
};

// Convert baseball coordinates to fan-shaped field coordinates
const baseballToFanField = (distance: number, direction: number, svgWidth: number, svgHeight: number) => {
  // Normalize direction to -45 to +45 degrees (left field to right field)
  let normalizedDirection = direction;

  // Clamp direction to foul territory limits
  normalizedDirection = Math.max(-45, Math.min(45, normalizedDirection));

  // Convert to radians
  const radians = (normalizedDirection * Math.PI) / 180;

  // Scale distance (max 450 feet for display)
  const maxDistance = 450;
  const scaledDistance = Math.min(distance, maxDistance);
  const distanceRatio = scaledDistance / maxDistance;

  // Calculate field dimensions
  const fieldRadius = Math.min(svgWidth, svgHeight) * 0.4;
  const homeX = svgWidth / 2;
  const homeY = svgHeight - 40;

  // Calculate position in fan shape
  const x = homeX + (distanceRatio * fieldRadius * Math.sin(radians));
  const y = homeY - (distanceRatio * fieldRadius * Math.cos(radians));

  return { x, y };
};

// Get hit color based on outcome (matching MLB spray chart style)
const getHitColor = (exitVelo: number, launchAngle: number, distance: number): string => {
  // Home runs - red
  if (distance >= 330 && launchAngle >= 15 && launchAngle <= 35) {
    return '#dc2626'; // Red for home runs
  }

  // Extra base hits (doubles/triples) - orange/red
  if (distance >= 250 && launchAngle >= 10 && launchAngle <= 30) {
    return '#ea580c'; // Orange-red for extra base hits
  }

  // Quality contact (line drives, hard hit) - blue
  if ((launchAngle >= 8 && launchAngle <= 25 && exitVelo >= 85) || exitVelo >= 95) {
    return '#2563eb'; // Blue for quality contact
  }

  // Weak contact - light blue
  if (exitVelo < 75 || distance < 150) {
    return '#60a5fa'; // Light blue for weak contact
  }

  // Ground balls - dark gray
  if (launchAngle < 8) {
    return '#374151'; // Dark gray for ground balls
  }

  // Default contact - medium blue
  return '#3b82f6'; // Medium blue for average contact
};

// Get hit outcome text
const getHitOutcome = (distance: number, angle: number): string => {
  if (distance >= 330 && angle >= 15 && angle <= 35) return 'Home Run';
  if (distance >= 250 && angle >= 10 && angle <= 30) return 'Double/Triple';
  if (angle >= 5 && angle <= 25 && distance >= 150) return 'Line Drive';
  if (angle < 10) return 'Ground Ball';
  if (angle > 35) return 'Fly Ball';
  return 'Hit';
};

const SprayChart: React.FC<SprayChartProps> = ({ data, selectedBatter }) => {
  const [hoveredHit, setHoveredHit] = useState<any>(null);
  const [tooltipPosition, setTooltipPosition] = useState({ x: 0, y: 0 });

  const batterData = data.filter(hit => hit.Batter === selectedBatter);

  if (batterData.length === 0) {
    return (
      <div className="w-full h-96 flex items-center justify-center text-white">
        No spray chart data available for selected batter
      </div>
    );
  }

  const svgWidth = 800;
  const svgHeight = 600;

  return (
    <div className="w-full h-96 relative rounded-lg overflow-hidden bg-white">
      <svg viewBox={`0 0 ${svgWidth} ${svgHeight}`} className="w-full h-full">
        {/* Definitions for gradients and patterns */}
        <defs>
          <radialGradient id="fieldGradient" cx="50%" cy="100%" r="70%">
            <stop offset="0%" stopColor="#f8f9fa" />
            <stop offset="100%" stopColor="#e9ecef" />
          </radialGradient>
        </defs>

        {/* Background */}
        <rect width={svgWidth} height={svgHeight} fill="url(#fieldGradient)" />

        {/* Fan-shaped field outline */}
        <path
          d={`M ${svgWidth/2} ${svgHeight - 40}
              L ${svgWidth/2 - svgWidth * 0.35} ${svgHeight - 40 - svgHeight * 0.35}
              A ${svgWidth * 0.35} ${svgHeight * 0.35} 0 0 1 ${svgWidth/2 + svgWidth * 0.35} ${svgHeight - 40 - svgHeight * 0.35}
              Z`}
          fill="#f8f9fa"
          stroke="#6b7280"
          strokeWidth="3"
        />

        {/* Foul lines */}
        <line
          x1={svgWidth/2}
          y1={svgHeight - 40}
          x2={svgWidth/2 - svgWidth * 0.35}
          y2={svgHeight - 40 - svgHeight * 0.35}
          stroke="#6b7280"
          strokeWidth="2"
        />
        <line
          x1={svgWidth/2}
          y1={svgHeight - 40}
          x2={svgWidth/2 + svgWidth * 0.35}
          y2={svgHeight - 40 - svgHeight * 0.35}
          stroke="#6b7280"
          strokeWidth="2"
        />

        {/* Distance arcs */}
        {[0.2, 0.4, 0.6, 0.8, 1.0].map((ratio, index) => {
          const radius = svgWidth * 0.35 * ratio;
          const distance = Math.round(450 * ratio);
          return (
            <g key={index}>
              <path
                d={`M ${svgWidth/2 - radius} ${svgHeight - 40 - radius}
                    A ${radius} ${radius} 0 0 1 ${svgWidth/2 + radius} ${svgHeight - 40 - radius}`}
                fill="none"
                stroke="#d1d5db"
                strokeWidth="1"
                strokeDasharray={index === 4 ? "none" : "3,3"}
              />
              {index > 0 && (
                <text
                  x={svgWidth/2}
                  y={svgHeight - 40 - radius + 15}
                  textAnchor="middle"
                  fontSize="10"
                  fill="#6b7280"
                  fontWeight="500"
                >
                  {distance}'
                </text>
              )}
            </g>
          );
        })}

        {/* Home plate marker */}
        <circle
          cx={svgWidth/2}
          cy={svgHeight - 40}
          r="4"
          fill="#374151"
          stroke="#1f2937"
          strokeWidth="2"
        />

        {/* Plot Hits */}
        {batterData.map((hit, index) => {
          const coords = baseballToFanField(hit.Distance, hit.Direction, svgWidth, svgHeight);

          // Only show hits within the fan-shaped field
          const fieldRadius = svgWidth * 0.35;
          const distanceFromHome = Math.sqrt(
            Math.pow(coords.x - svgWidth/2, 2) + Math.pow(coords.y - (svgHeight - 40), 2)
          );

          if (distanceFromHome > fieldRadius) return null;

          const color = getHitColor(hit.ExitSpeed || 0, hit.Angle || 0, hit.Distance);
          const isHovered = hoveredHit === hit;

          return (
            <circle
              key={index}
              cx={coords.x}
              cy={coords.y}
              r={isHovered ? 6 : 4}
              fill={color}
              stroke={isHovered ? "#1f2937" : "none"}
              strokeWidth={isHovered ? 2 : 0}
              opacity={isHovered ? 1 : 0.8}
              className="cursor-pointer transition-all duration-200"
              onMouseEnter={(e) => {
                setHoveredHit(hit);
                const rect = e.currentTarget.getBoundingClientRect();
                const svgRect = e.currentTarget.closest('svg')?.getBoundingClientRect();
                if (svgRect) {
                  setTooltipPosition({
                    x: rect.left - svgRect.left + rect.width / 2,
                    y: rect.top - svgRect.top
                  });
                }
              }}
              onMouseLeave={() => {
                setHoveredHit(null);
              }}
            />
          );
        })}

        {/* Position Labels */}
        <text x={svgWidth/2 - svgWidth * 0.25} y={svgHeight - 40 - svgHeight * 0.25} fill="#374151" fontSize="14" fontWeight="bold">LF</text>
        <text x={svgWidth/2} y={svgHeight - 40 - svgHeight * 0.32} fill="#374151" fontSize="14" fontWeight="bold" textAnchor="middle">CF</text>
        <text x={svgWidth/2 + svgWidth * 0.25} y={svgHeight - 40 - svgHeight * 0.25} fill="#374151" fontSize="14" fontWeight="bold">RF</text>
      </svg>

      {/* Enhanced Tooltip */}
      {hoveredHit && (
        <div
          className="absolute bg-white border-2 border-gray-300 rounded-lg shadow-xl text-gray-800 z-50 pointer-events-none"
          style={{
            left: `${tooltipPosition.x}px`,
            top: `${tooltipPosition.y}px`,
            transform: 'translate(-50%, -100%)',
            animation: 'fadeInScale 200ms ease-out'
          }}
        >
          <div className="p-4 min-w-[220px]">
            <div className="flex items-center gap-2 mb-3">
              <div
                className="w-3 h-3 rounded-full shadow-lg"
                style={{
                  backgroundColor: getHitColor(hoveredHit.ExitSpeed || 0, hoveredHit.Angle || 0, hoveredHit.Distance)
                }}
              ></div>
              <h4 className="font-bold text-lg text-gray-800">Hit Details</h4>
            </div>

            <div className="space-y-2">
              <div className="flex justify-between items-center">
                <span className="text-gray-600 text-sm">Exit Velocity:</span>
                <span className="font-semibold text-gray-800">{hoveredHit.ExitSpeed?.toFixed(1) || 'N/A'} mph</span>
              </div>

              <div className="flex justify-between items-center">
                <span className="text-gray-600 text-sm">Launch Angle:</span>
                <span className="font-semibold text-gray-800">{hoveredHit.Angle?.toFixed(1) || 'N/A'}°</span>
              </div>

              <div className="flex justify-between items-center">
                <span className="text-gray-600 text-sm">Distance:</span>
                <span className="font-semibold text-gray-800">{hoveredHit.Distance?.toFixed(0) || 'N/A'} ft</span>
              </div>

              <div className="flex justify-between items-center">
                <span className="text-gray-600 text-sm">Direction:</span>
                <span className="font-semibold text-gray-800">{hoveredHit.Direction?.toFixed(1) || 'N/A'}°</span>
              </div>

              <div className="border-t border-gray-300 pt-2 mt-3">
                <div className="flex justify-between items-center">
                  <span className="text-gray-500 text-xs">Outcome:</span>
                  <span className="font-medium text-gray-800">
                    {getHitOutcome(hoveredHit.Distance || 0, hoveredHit.Angle || 0)}
                  </span>
                </div>
              </div>
            </div>
          </div>

          {/* Tooltip Arrow */}
          <div className="absolute top-full left-1/2 transform -translate-x-1/2">
            <div className="w-0 h-0 border-l-8 border-r-8 border-t-8 border-l-transparent border-r-transparent border-t-gray-300"></div>
          </div>
        </div>
      )}

      {/* Legend */}
      <div className="absolute top-4 right-4 bg-white/90 backdrop-blur-sm border border-gray-300 rounded-lg p-3 shadow-lg">
        <h4 className="font-bold text-sm text-gray-800 mb-2">Hit Types</h4>
        <div className="space-y-1 text-xs">
          <div className="flex items-center gap-2">
            <div className="w-3 h-3 rounded-full bg-red-600"></div>
            <span className="text-gray-700">Home Run</span>
          </div>
          <div className="flex items-center gap-2">
            <div className="w-3 h-3 rounded-full bg-orange-600"></div>
            <span className="text-gray-700">Extra Base Hit</span>
          </div>
          <div className="flex items-center gap-2">
            <div className="w-3 h-3 rounded-full bg-blue-600"></div>
            <span className="text-gray-700">Quality Contact</span>
          </div>
          <div className="flex items-center gap-2">
            <div className="w-3 h-3 rounded-full bg-blue-400"></div>
            <span className="text-gray-700">Average Contact</span>
          </div>
          <div className="flex items-center gap-2">
            <div className="w-3 h-3 rounded-full bg-gray-600"></div>
            <span className="text-gray-700">Ground Ball</span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SprayChart;
