import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, DialogHeader, DialogTitle } from '../../../components/ui/dialog';
import { Button } from '../../../components/ui/button';
import { Checkbox } from '../../../components/ui/checkbox';
import { Textarea } from '../../../components/ui/textarea';
import ReportTemplate from './ReportTemplate';
import { PDFViewer, pdf } from '@react-pdf/renderer';
import { 
  calculateQualityOfContactScore,
  calculateRoomForErrorScore,
  calculateLaunchAngleScore,
  calculateTotalHitScore,
  LEVEL_BENCHMARKS,
  DEFAULT_PARAMS
} from '../Hitting/HittingMetricsDashboard';

import { HittingData } from '../Hitting/HittingMetricsDashboard';

import TemplateEditor from './TemplateEditor';

interface PdfBuilderModalProps {
  isOpen: boolean;
  onClose: () => void;
  players: string[];
  metrics: string[];
  data: HittingData[];
  selectedLevel: string;
  DEFAULT_PARAMS: typeof DEFAULT_PARAMS;  // ✅ Accept DEFAULT_PARAMS as a prop
  calculatedMetrics: Record<string, number>;
}



const PdfBuilderModal = ({ isOpen, onClose, players, metrics, calculatedMetrics, data, selectedLevel }: PdfBuilderModalProps) => {
  const [selectedPlayers, setSelectedPlayers] = useState<string[]>([]);
  const [selectedMetrics, setSelectedMetrics] = useState<Record<string, boolean>>(metrics.reduce((acc, metric) => {
    acc[metric] = true;
    return acc;
  }, {} as Record<string, boolean>));
  const [notes, setNotes] = useState('');
  const [layout, setLayout] =useState([]);
  const [isPreview, setIsPreview] = useState(false);

  useEffect(() => {
    const { getStorageData, STORAGE_KEYS } = require('@/lib/storage');
    const savedTemplate = getStorageData(STORAGE_KEYS.REPORT_TEMPLATE, []);
    if (savedTemplate && savedTemplate.length > 0) {
      setLayout(savedTemplate);
    }
  }, []);
  

  const handleGeneratePDF = async () => {
    for (const player of selectedPlayers) {
      const selectedPlayerData = data.find(hit => hit.Batter === player);
  
      if (!selectedPlayerData) {
        console.warn(`No data found for player: ${player}`);
        continue; // Skip if no data is found for the player
      }
  
      const metricsData = Object.entries(selectedMetrics)
      .filter(([_, isSelected]) => isSelected)
      .map(([metric]) => ({
        label: metric,
        value: typeof calculatedMetrics[metric] === "number" ? calculatedMetrics[metric] : 0 // Ensure a number
      }));
    
    
    
      console.log("Metrics data in PDF:", calculatedMetrics);
      console.log("Selected Metrics:", selectedMetrics);
      console.log("Selected Player Data:", selectedPlayerData)
  
      const blob = await pdf(
        <ReportTemplate
          playerName={player}
          metrics={metricsData}
          notes={notes}
        />
      ).toBlob();
  
      const link = document.createElement('a');
      link.href = URL.createObjectURL(blob);
      link.download = `${player}_Report.pdf`;
      link.click();
    }
  };
  

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-[90vw] max-h-[80vh] flex flex-col overflow-auto">
        <DialogHeader>
          <DialogTitle>Build Player Report</DialogTitle>
        </DialogHeader>

    {/* ✅ Drag-and-Drop Layout Editor */}
    <TemplateEditor onSave={setLayout} calculatedMetrics={calculatedMetrics} />


        {isPreview ? (
          <PDFViewer width="100%" height="800px" style={{ border: '1px solid #e5e7eb', minHeight: '70vh' }}>

              <ReportTemplate
                  playerName={selectedPlayers[0] || ''}
                  metrics={Object.entries(selectedMetrics)
                    .filter(([_, isSelected]) => isSelected)
                    .map(([metric]) => {
                      let value = 0;
                      let gaugeImage = '';

                      // Get the correct player data
                      const selectedPlayerData = data.find(hit => hit.Batter === selectedPlayers[0]);

                      if (selectedPlayerData) {
                        switch (metric) {
                          case 'Quality of Contact':
                            value = calculateQualityOfContactScore(selectedPlayerData, LEVEL_BENCHMARKS[selectedLevel]);
                            gaugeImage = '/gauge_quality.png'; // Placeholder for gauge image
                            break;
                          case 'Launch Angle':
                            value = calculateLaunchAngleScore(selectedPlayerData, DEFAULT_PARAMS.launchAngle, selectedLevel as 'Professional' | 'College' | 'High School');
                            gaugeImage = '/gauge_launch.png';
                            break;
                          case 'Room for Error':
                            value = calculateRoomForErrorScore(data, data.length, DEFAULT_PARAMS.roomForError, selectedLevel as 'Professional' | 'College' | 'High School');
                            gaugeImage = '/gauge_error.png';
                            break;
                          case 'Total Score':
                            value = calculateTotalHitScore(selectedPlayerData, data.length, data);
                            gaugeImage = '/gauge_total.png';
                            break;
                        }
                      }

                      return { label: metric, value, gaugeImage };
                    })}
                  notes={notes}
                />
              </PDFViewer>


        ) : (
          <div className="grid grid-cols-2 gap-6 py-4">
            <div className="col-span-2 mb-4">
              <h3 className="font-semibold mb-4">Select Player</h3>
              <div className="max-h-40 overflow-y-auto border rounded-md p-2">
                {players.map((name) => (
                  <div key={name} className="flex items-center space-x-2 py-1">
                    <Checkbox
                      checked={selectedPlayers.includes(name)}
                      onCheckedChange={(checked) => {
                        if (checked) {
                          setSelectedPlayers((prev) => [...prev, name]);
                        } else {
                          setSelectedPlayers((prev) => prev.filter((p) => p !== name));
                        }
                      }}
                    />
                    <label className="text-sm font-medium">{name}</label>
                  </div>
                ))}
              </div>
            </div>

            <div className="space-y-4">
              <h3 className="font-semibold mb-4">Select Metrics to Include</h3>
              <div className="space-y-2">
                {Object.entries(selectedMetrics).map(([metric, isSelected]) => (
                  <div key={metric} className="flex items-center space-x-2">
                    <Checkbox
                      checked={isSelected}
                      onCheckedChange={(checked) =>
                        setSelectedMetrics((prev) => ({ ...prev, [metric]: !!checked }))
                      }
                    />
                    <label className="text-sm font-medium">{metric}</label>
                  </div>
                ))}
              </div>
            </div>

            <div className="space-y-4">
              <h3 className="font-semibold mb-4">Add Custom Notes</h3>
              <Textarea
                value={notes}
                onChange={(e) => setNotes(e.target.value)}
                placeholder="Add explanations, recommendations, or other notes..."
                className="h-[200px]"
              />
            </div>
          </div>
        )}

        <div className="flex justify-end gap-2 mt-6">
          <Button variant="outline" onClick={onClose}>
            Cancel
          </Button>
          {selectedPlayers.length > 0 && (
            <>
              <Button
                variant="outline"
                onClick={() => setIsPreview(!isPreview)}
              >
                {isPreview ? 'Back to Edit' : 'Preview PDF'}
              </Button>
              <Button onClick={handleGeneratePDF}>
                Generate PDF
              </Button>
            </>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default PdfBuilderModal;