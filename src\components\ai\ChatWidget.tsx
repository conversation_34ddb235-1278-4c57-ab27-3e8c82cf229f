"use client";

import React, { useState, useRef, useEffect } from 'react';
import { Card, CardHeader, CardT<PERSON>le, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Send, MessageCircle, X, Loader2 } from 'lucide-react';
import { ChatMessage, AIResponse, QueryRequest } from '@/types/ai';
import ChartRenderer from './ChartRenderer';
import { getStorageData, STORAGE_KEYS } from '@/lib/storage';
import { HittingData } from '@/components/dashboard/Hitting/HittingMetricsDashboard';
import { PitchData } from '@/components/dashboard/PitchingMetricsDashboard';

interface ChatWidgetProps {
  playerId: string;
  playerName?: string;
  dataType?: 'hitting' | 'pitching' | 'both';
  level?: 'Professional' | 'College' | 'High School';
  className?: string;
}

export default function ChatWidget({
  playerId,
  playerName,
  dataType = 'both',
  level = 'Professional',
  className = ''
}: ChatWidgetProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [inputValue, setInputValue] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);

  // Scroll to bottom when new messages are added
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  // Focus input when chat opens
  useEffect(() => {
    if (isOpen && inputRef.current) {
      inputRef.current.focus();
    }
  }, [isOpen]);

  // Initialize with welcome message
  useEffect(() => {
    if (messages.length === 0) {
      const welcomeMessage: ChatMessage = {
        id: 'welcome',
        role: 'assistant',
        content: `Hi! I'm your baseball analytics AI assistant. Ask me anything about ${playerName || playerId}'s performance!`,
        timestamp: new Date()
      };
      setMessages([welcomeMessage]);
    }
  }, [playerId, playerName, messages.length]);

  const handleSendMessage = async () => {
    if (!inputValue.trim() || isLoading) return;

    const userMessage: ChatMessage = {
      id: Date.now().toString(),
      role: 'user',
      content: inputValue.trim(),
      timestamp: new Date()
    };

    const loadingMessage: ChatMessage = {
      id: (Date.now() + 1).toString(),
      role: 'assistant',
      content: 'Analyzing your performance data... This may take 10-15 seconds.',
      timestamp: new Date(),
      isLoading: true
    };

    setMessages(prev => [...prev, userMessage, loadingMessage]);
    setInputValue('');
    setIsLoading(true);

    try {
      // Get current data from localStorage
      const hittingData: HittingData[] = getStorageData(STORAGE_KEYS.HITTING_DATA, []);
      const pitchingData: PitchData[] = getStorageData(STORAGE_KEYS.PITCHING_DATA, []);

      const queryRequest = {
        playerId,
        question: inputValue.trim(),
        dataType,
        level,
        hittingData,
        pitchingData
      };

      const response = await fetch('/api/query', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(queryRequest),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const aiResponse: AIResponse = await response.json();

      const assistantMessage: ChatMessage = {
        id: (Date.now() + 2).toString(),
        role: 'assistant',
        content: aiResponse.text,
        timestamp: new Date(),
        chart: aiResponse.chart
      };

      // Replace loading message with actual response
      setMessages(prev => prev.slice(0, -1).concat(assistantMessage));

    } catch (error) {
      console.error('Error sending message:', error);
      
      const errorMessage: ChatMessage = {
        id: (Date.now() + 2).toString(),
        role: 'assistant',
        content: 'Sorry, I encountered an error processing your question. Please try again.',
        timestamp: new Date()
      };

      setMessages(prev => prev.slice(0, -1).concat(errorMessage));
    } finally {
      setIsLoading(false);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  const suggestedQuestions = [
    "What was my average exit velocity?",
    "How does my performance compare to my level?",
    "Show me my velocity distribution",
    "What are my strengths and weaknesses?"
  ];

  if (!isOpen) {
    return (
      <Button
        onClick={() => setIsOpen(true)}
        className={`fixed bottom-4 right-4 rounded-full w-14 h-14 shadow-lg bg-orange-500 hover:bg-orange-600 ${className}`}
        size="lg"
      >
        <MessageCircle className="w-6 h-6" />
      </Button>
    );
  }

  return (
    <Card className={`fixed bottom-4 right-4 w-96 h-[500px] shadow-xl z-50 flex flex-col ${className}`}>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2 bg-orange-500 text-white rounded-t-lg">
        <CardTitle className="text-lg font-semibold">
          Analytics AI
        </CardTitle>
        <Button
          variant="ghost"
          size="sm"
          onClick={() => setIsOpen(false)}
          className="text-white hover:bg-orange-600"
        >
          <X className="w-4 h-4" />
        </Button>
      </CardHeader>
      
      <CardContent className="flex-1 flex flex-col p-0">
        {/* Messages */}
        <div className="flex-1 overflow-y-auto p-4 space-y-4">
          {messages.map((message) => (
            <div
              key={message.id}
              className={`flex ${message.role === 'user' ? 'justify-end' : 'justify-start'}`}
            >
              <div
                className={`max-w-[80%] rounded-lg p-3 ${
                  message.role === 'user'
                    ? 'bg-orange-500 text-white'
                    : 'bg-gray-100 text-gray-900'
                }`}
              >
                {message.isLoading ? (
                  <div className="flex items-center space-x-2">
                    <Loader2 className="w-4 h-4 animate-spin" />
                    <span>{message.content}</span>
                  </div>
                ) : (
                  <>
                    <p className="text-sm">{message.content}</p>
                    {message.chart && (
                      <div className="mt-3">
                        <ChartRenderer chart={message.chart} />
                      </div>
                    )}
                  </>
                )}
              </div>
            </div>
          ))}
          <div ref={messagesEndRef} />
        </div>

        {/* Suggested Questions */}
        {messages.length <= 1 && (
          <div className="px-4 py-2 border-t">
            <p className="text-xs text-gray-500 mb-2">Try asking:</p>
            <div className="space-y-1">
              {suggestedQuestions.slice(0, 2).map((question, index) => (
                <button
                  key={index}
                  onClick={() => setInputValue(question)}
                  className="text-xs text-orange-600 hover:text-orange-800 block w-full text-left"
                >
                  "{question}"
                </button>
              ))}
            </div>
          </div>
        )}

        {/* Input */}
        <div className="p-4 border-t">
          <div className="flex space-x-2">
            <input
              ref={inputRef}
              type="text"
              value={inputValue}
              onChange={(e) => setInputValue(e.target.value)}
              onKeyPress={handleKeyPress}
              placeholder="Ask about performance..."
              className="flex-1 px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-orange-500"
              disabled={isLoading}
            />
            <Button
              onClick={handleSendMessage}
              disabled={!inputValue.trim() || isLoading}
              size="sm"
              className="bg-orange-500 hover:bg-orange-600"
            >
              <Send className="w-4 h-4" />
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
