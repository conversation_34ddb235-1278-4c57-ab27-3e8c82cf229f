/**
 * Server-safe calculation functions for baseball analytics
 * These are duplicated from the client components to avoid Next.js 13+ server/client boundary issues
 */

import { HittingData } from '@/components/dashboard/Hitting/HittingMetricsDashboard';

// Types
interface LevelBenchmarks {
  minOptimalEV: number;
  maxOptimalEV: number;
  belowOptimalStart: number;
  belowOptimalEnd: number;
  optimalStart: number;
  optimalEnd: number;
  aboveAverageStart: number;
  aboveAverageEnd: number;
  perfectStart: number;
  perfectEnd: number;
}

interface QualityOfContactParams {
  weight_ev: number;
  weight_sr: number;
  weight_sa: number;
}

interface RoomForErrorParams {
  max_spread_score: number;
  max_ev_score: number;
  max_consistency_score: number;
  max_line_drive_score: number;
  line_drive_min_angle: number;
  line_drive_max_angle: number;
  min_ev_for_line_drive: number;
  max_possible_stddev_ev: number;
  consistency_scaling_constant: number;
  spread_scaling_constant: number;
  levelParams: {
    [key: string]: {
      minEffectiveEV: number;
      optimalEV: number;
      lineDriveAngles: [number, number];
      maxSpreadInches: number;
      minMissQualityForSpread: number;
    };
  };
}

interface LaunchAngleParams {
  la_weight: number;
  ev_weight: number;
  la_scaling_factor: number;
  min_la_score: number;
  line_drive_bonus: number;
}

// Constants
export const LEVEL_BENCHMARKS: Record<string, LevelBenchmarks> = {
  Professional: {
    minOptimalEV: 92,
    maxOptimalEV: 119,
    belowOptimalStart: 0,
    belowOptimalEnd: 88.99,
    optimalStart: 89,
    optimalEnd: 94.99,
    aboveAverageStart: 95,
    aboveAverageEnd: 104.99,
    perfectStart: 105,
    perfectEnd: 119
  },
  College: {
    minOptimalEV: 85,
    maxOptimalEV: 110,
    belowOptimalStart: 0,
    belowOptimalEnd: 79.99,
    optimalStart: 80,
    optimalEnd: 89.99,
    aboveAverageStart: 90,
    aboveAverageEnd: 99.99,
    perfectStart: 100,
    perfectEnd: 110
  },
  'High School': {
    minOptimalEV: 78,
    maxOptimalEV: 105,
    belowOptimalStart: 0,
    belowOptimalEnd: 74.99,
    optimalStart: 75,
    optimalEnd: 84.99,
    aboveAverageStart: 85,
    aboveAverageEnd: 94.99,
    perfectStart: 95,
    perfectEnd: 105
  }
};

export const DEFAULT_PARAMS = {
  roomForError: {
    max_spread_score: 20,
    max_ev_score: 30,
    max_consistency_score: 20,
    max_line_drive_score: 20,
    line_drive_min_angle: 10,
    line_drive_max_angle: 25,
    min_ev_for_line_drive: 90,
    max_possible_stddev_ev: 20,
    consistency_scaling_constant: 0.2876820725,
    spread_scaling_constant: 0.2876820725,
    levelParams: {
      Professional: {
        minEffectiveEV: 88,
        optimalEV: 95,
        lineDriveAngles: [10, 25] as [number, number],
        maxSpreadInches: 10,
        minMissQualityForSpread: 0.5
      },
      College: {
        minEffectiveEV: 83,
        optimalEV: 90,
        lineDriveAngles: [8, 28] as [number, number],
        maxSpreadInches: 14,
        minMissQualityForSpread: 0.4
      },
      'High School': {
        minEffectiveEV: 78,
        optimalEV: 85,
        lineDriveAngles: [6, 30] as [number, number],
        maxSpreadInches: 18,
        minMissQualityForSpread: 0.3
      }
    }
  },
  qualityOfContact: {
    weight_ev: 0.7,
    weight_sr: 0.2,
    weight_sa: 0.1
  },
  launchAngle: {
    la_weight: 0.8,
    ev_weight: 0.2,
    la_scaling_factor: 20,
    min_la_score: 0,
    line_drive_bonus: 20
  }
};

// Helper functions
const calculateEVScore = (exitSpeed: number, benchmarks: LevelBenchmarks, level: string): number => {
  if (!exitSpeed || !benchmarks) return 0;
  
  if (exitSpeed >= benchmarks.perfectStart) return 100;
  if (exitSpeed >= benchmarks.aboveAverageStart) return 80 + ((exitSpeed - benchmarks.aboveAverageStart) / (benchmarks.perfectStart - benchmarks.aboveAverageStart)) * 20;
  if (exitSpeed >= benchmarks.optimalStart) return 60 + ((exitSpeed - benchmarks.optimalStart) / (benchmarks.aboveAverageStart - benchmarks.optimalStart)) * 20;
  if (exitSpeed >= benchmarks.belowOptimalEnd) return 40 + ((exitSpeed - benchmarks.belowOptimalEnd) / (benchmarks.optimalStart - benchmarks.belowOptimalEnd)) * 20;
  
  return Math.max(0, (exitSpeed / benchmarks.belowOptimalEnd) * 40);
};

const calculateSpinRateScore = (spinRate: number, exitSpeed: number): number => {
  if (!spinRate || !exitSpeed) return 50; // Default score if no spin rate data
  
  const idealSpinRate = 2200 + (exitSpeed - 90) * 10; // Rough approximation
  const deviation = Math.abs(spinRate - idealSpinRate);
  const maxDeviation = 800;
  
  return Math.max(0, 100 - (deviation / maxDeviation) * 100);
};

const calculateSpinAxisScore = (spinAxis: number, exitSpeed: number): number => {
  if (!spinAxis || !exitSpeed) return 50; // Default score if no spin axis data
  
  const idealSpinAxis = 180; // Backspin
  const deviation = Math.abs(spinAxis - idealSpinAxis);
  const maxDeviation = 90;
  
  return Math.max(0, 100 - (deviation / maxDeviation) * 100);
};

// Main calculation functions
export const calculateQualityOfContactScore = (
  hits: HittingData[],
  benchmarks: LevelBenchmarks,
  effectiveLevel: 'Professional' | 'College' | 'High School',
  params: QualityOfContactParams = DEFAULT_PARAMS.qualityOfContact
): number => {
  if (!hits.length) return 0;
  
  const hitScores = hits.map(hit => {
    const evScore = calculateEVScore(hit.ExitSpeed, benchmarks, effectiveLevel);
    const srScore = calculateSpinRateScore(hit.HitSpinRate, hit.ExitSpeed);
    const saScore = calculateSpinAxisScore(hit.HitSpinAxis, hit.ExitSpeed);
    return (evScore * params.weight_ev) + (srScore * params.weight_sr) + (saScore * params.weight_sa);
  });
  
  return hitScores.reduce((sum, score) => sum + score, 0) / hitScores.length;
};

export const calculateRoomForErrorScore = (
  hits: HittingData[],
  validHits: number,
  params: RoomForErrorParams,
  effectiveLevel: 'Professional' | 'College' | 'High School'
): number => {
  if (validHits <= 1) return 0;
  
  const contactPositions = hits.filter(hit => hit.ContactPositionY !== undefined).map(hit => hit.ContactPositionY!);
  const posYRange = contactPositions.length > 0 ? Math.max(...contactPositions) - Math.min(...contactPositions) : 0;
  const validExitSpeeds = hits.filter(hit => hit.ExitSpeed).map(hit => hit.ExitSpeed);
  const evStandardDeviation = validExitSpeeds.length > 1 ? Math.sqrt(validExitSpeeds.reduce((sum, ev) => {
    const mean = validExitSpeeds.reduce((s, v) => s + v, 0) / validExitSpeeds.length;
    return sum + Math.pow(ev - mean, 2);
  }, 0) / (validExitSpeeds.length - 1)) : 0;
  
  const spreadScore = Math.min(params.max_spread_score, posYRange * 12 * 2);
  const consistencyScore = Math.max(0, params.max_consistency_score * (1 - (evStandardDeviation / params.max_possible_stddev_ev) * params.consistency_scaling_constant));
  const lineDriveHits = hits.filter(hit => hit.Angle >= params.line_drive_min_angle && hit.Angle <= params.line_drive_max_angle && hit.ExitSpeed >= params.min_ev_for_line_drive);
  const lineDriveScore = Math.min(params.max_line_drive_score, (lineDriveHits.length / validHits) * params.max_line_drive_score * 2);
  
  return Math.min(100, spreadScore + consistencyScore + lineDriveScore);
};

export const calculateLaunchAngleScore = (
  hit: HittingData,
  params: LaunchAngleParams = DEFAULT_PARAMS.launchAngle,
  effectiveLevel: 'Professional' | 'College' | 'High School'
): number => {
  if (!hit.Angle || !hit.ExitSpeed) return 0;
  
  const idealMin = 5, idealMax = 20;
  let laProxScore = 100;
  
  if (hit.Angle < idealMin || hit.Angle > idealMax) {
    const deviation = hit.Angle < idealMin ? idealMin - hit.Angle : hit.Angle - idealMax;
    laProxScore = Math.max(params.min_la_score, 100 * Math.exp(-0.15 * deviation));
  }
  
  const benchmarks = LEVEL_BENCHMARKS[effectiveLevel];
  let evProxScore = 100;
  if (hit.ExitSpeed < benchmarks.minOptimalEV || hit.ExitSpeed > benchmarks.maxOptimalEV) {
    const deviation = hit.ExitSpeed < benchmarks.minOptimalEV ? 
      benchmarks.minOptimalEV - hit.ExitSpeed : 
      hit.ExitSpeed - benchmarks.maxOptimalEV;
    evProxScore = Math.max(params.min_la_score, 100 - (deviation / 50) * 100);
  }
  
  const combinedScore = (laProxScore * params.la_weight + evProxScore * params.ev_weight);
  const hardHitBonus = (hit.Angle >= 8 && hit.Angle <= 32 && hit.ExitSpeed >= 95) ? 20 : 0;
  
  return Math.min(100, combinedScore + hardHitBonus);
};

export const calculateTotalHitScore = (
  hits: HittingData[],
  validHits: number,
  allHits: HittingData[],
  effectiveLevel: 'Professional' | 'College' | 'High School'
): number => {
  if (!hits.length || validHits === 0) return 0;
  
  const benchmarks = LEVEL_BENCHMARKS[effectiveLevel];
  if (!benchmarks) return 0;
  
  const qualityOfContactScore = calculateQualityOfContactScore(hits, benchmarks, effectiveLevel);
  const roomForErrorScore = calculateRoomForErrorScore(allHits, validHits, DEFAULT_PARAMS.roomForError, effectiveLevel);
  
  // Calculate aggregated hit for launch angle
  const avgExitSpeed = hits.reduce((sum, hit) => sum + hit.ExitSpeed, 0) / hits.length;
  const avgAngle = hits.reduce((sum, hit) => sum + hit.Angle, 0) / hits.length;
  const avgSpinRate = hits.reduce((sum, hit) => sum + (hit.HitSpinRate || 0), 0) / hits.length;
  const aggregatedHit: HittingData = { 
    ...hits[0], 
    ExitSpeed: avgExitSpeed, 
    Angle: avgAngle, 
    HitSpinRate: avgSpinRate, 
    Level: effectiveLevel 
  };
  
  const launchAngleScore = calculateLaunchAngleScore(aggregatedHit, DEFAULT_PARAMS.launchAngle, effectiveLevel);
  
  return Math.min(100, (qualityOfContactScore * 0.4) + (roomForErrorScore * 0.3) + (launchAngleScore * 0.3));
};

// Pitching calculations
export interface PitchData {
  AutoPitchType: string;
  Pitcher: string;
  RelSpeed: number;
  SpinRate: number;
  VertBreak: number;
  HorzBreak: number;
  RelSide: number;
  RelHeight: number;
  Extension: number;
}

export const calculateAveragePitch = (pitches: PitchData[]): PitchData => ({
  AutoPitchType: pitches[0].AutoPitchType,
  Pitcher: pitches[0].Pitcher,
  RelSpeed: pitches.reduce((sum, p) => sum + p.RelSpeed, 0) / pitches.length,
  SpinRate: pitches.reduce((sum, p) => sum + p.SpinRate, 0) / pitches.length,
  VertBreak: pitches.reduce((sum, p) => sum + p.VertBreak, 0) / pitches.length,
  HorzBreak: pitches.reduce((sum, p) => sum + p.HorzBreak, 0) / pitches.length,
  RelSide: pitches.reduce((sum, p) => sum + p.RelSide, 0) / pitches.length,
  RelHeight: pitches.reduce((sum, p) => sum + p.RelHeight, 0) / pitches.length,
  Extension: pitches.reduce((sum, p) => sum + p.Extension, 0) / pitches.length
});

export const calculateArsenalScore = (pitches: PitchData[]): number => {
  if (pitches.length < 2) return 0;

  const pitchTypes = [...new Set(pitches.map(p => p.AutoPitchType))];
  const pitchMetrics = pitchTypes.map(type => {
    const typePitches = pitches.filter(p => p.AutoPitchType === type);
    return {
      type,
      avgVelocity: typePitches.reduce((sum, p) => sum + p.RelSpeed, 0) / typePitches.length,
      avgVertBreak: typePitches.reduce((sum, p) => sum + p.VertBreak, 0) / typePitches.length,
      avgHorzBreak: typePitches.reduce((sum, p) => sum + p.HorzBreak, 0) / typePitches.length,
    };
  });

  // Simple arsenal score calculation
  let score = 100;

  // Velocity separation
  const velocities = pitchMetrics.map(m => m.avgVelocity);
  const maxVelDiff = Math.max(...velocities) - Math.min(...velocities);
  if (maxVelDiff < 5) score -= 20;

  // Movement variety
  const movements = pitchMetrics.map(m => Math.sqrt(m.avgVertBreak ** 2 + m.avgHorzBreak ** 2));
  const avgMovement = movements.reduce((sum, m) => sum + m, 0) / movements.length;
  if (avgMovement < 10) score -= 15;

  return Math.max(0, score);
};
