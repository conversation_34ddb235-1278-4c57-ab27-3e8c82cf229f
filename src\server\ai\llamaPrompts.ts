import { PlayerMetrics, PromptTemplate } from '@/types/ai';

export const SYSTEM_PROMPT = `You are a baseball analytics expert. Answer questions about player performance data.

Respond with valid JSON only:
{
  "text": "your answer here"
}

Keep answers concise and include specific numbers from the data.`;

export const buildPrompt = (metrics: PlayerMetrics, question: string): string => {
  // Simplified metrics for shorter prompt
  let dataContext = '';

  if (metrics.hitting) {
    dataContext += `Hitting: ${metrics.hitting.totalHits} hits, avg exit velocity ${metrics.hitting.avgExitVelocity} mph, avg launch angle ${metrics.hitting.avgLaunchAngle}°, total score ${metrics.hitting.totalScore}. `;
  }

  if (metrics.pitching) {
    dataContext += `Pitching: ${metrics.pitching.totalPitches} pitches, arsenal score ${metrics.pitching.arsenalScore}. `;
  }

  return `${SYSTEM_PROMPT}

Player: ${metrics.playerName} (${metrics.level} level)
Data: ${dataContext}

Question: ${question}

JSON response:`;
};

// Specific prompt templates for different types of questions
export const PROMPT_TEMPLATES = {
  exitVelocity: (metrics: PlayerMetrics) => ({
    context: `Player has ${metrics.hitting?.totalHits || 0} recorded hits with average exit velocity of ${metrics.hitting?.avgExitVelocity || 0} mph`,
    suggestions: [
      "What was my average exit velocity?",
      "How does my exit velocity compare to my level?",
      "Show me my exit velocity distribution"
    ]
  }),

  pitchVelocity: (metrics: PlayerMetrics) => ({
    context: `Player has ${metrics.pitching?.totalPitches || 0} recorded pitches across ${metrics.pitching?.pitchTypes?.length || 0} pitch types`,
    suggestions: [
      "What's my fastest pitch velocity?",
      "Compare my pitch velocities by type",
      "How does my velocity compare to team average?"
    ]
  }),

  arsenal: (metrics: PlayerMetrics) => ({
    context: `Player's arsenal score is ${metrics.pitching?.arsenalScore || 0} with pitch types: ${metrics.pitching?.pitchTypes?.map(p => p.type).join(', ') || 'none'}`,
    suggestions: [
      "Analyze my pitch arsenal",
      "What's my strongest pitch type?",
      "How can I improve my arsenal score?"
    ]
  }),

  hitting: (metrics: PlayerMetrics) => ({
    context: `Player's hitting scores - Quality: ${metrics.hitting?.qualityOfContactScore || 0}, Launch Angle: ${metrics.hitting?.launchAngleScore || 0}, Room for Error: ${metrics.hitting?.roomForErrorScore || 0}`,
    suggestions: [
      "Analyze my hitting performance",
      "What's my strongest hitting metric?",
      "How can I improve my hitting scores?"
    ]
  })
};

// Helper function to generate contextual suggestions based on available data
export const generateSuggestions = (metrics: PlayerMetrics): string[] => {
  const suggestions: string[] = [];
  
  if (metrics.hitting) {
    suggestions.push(
      "What was my average exit velocity?",
      "Analyze my hitting performance",
      "Show me my launch angle distribution"
    );
  }
  
  if (metrics.pitching) {
    suggestions.push(
      "What's my fastest pitch velocity?",
      "Analyze my pitch arsenal",
      "Compare my pitch velocities by type"
    );
  }
  
  // General questions that work for both
  suggestions.push(
    "How do I compare to other players at my level?",
    "What are my strengths and weaknesses?",
    "Show me my performance trends"
  );
  
  return suggestions;
};

// Validation function to ensure metrics have required data
export const validateMetricsForQuery = (metrics: PlayerMetrics, question: string): boolean => {
  const lowerQuestion = question.toLowerCase();

  // Check if question is specifically about hitting but no hitting data exists
  if ((lowerQuestion.includes('exit') || lowerQuestion.includes('launch') || lowerQuestion.includes('contact')) && !metrics.hitting) {
    return false;
  }

  // Check if question is specifically about pitching but no pitching data exists
  if ((lowerQuestion.includes('pitch') || lowerQuestion.includes('arsenal') || lowerQuestion.includes('spin rate') || lowerQuestion.includes('break')) && !metrics.pitching) {
    return false;
  }

  // For "velocity" questions, check if we have any relevant data
  if (lowerQuestion.includes('velocity')) {
    // If it mentions "exit velocity", need hitting data
    if (lowerQuestion.includes('exit') && !metrics.hitting) {
      return false;
    }
    // If it mentions "pitch velocity", need pitching data
    if (lowerQuestion.includes('pitch') && !metrics.pitching) {
      return false;
    }
    // If just "velocity" without context, accept if we have any data
    if (!lowerQuestion.includes('exit') && !lowerQuestion.includes('pitch') && !metrics.hitting && !metrics.pitching) {
      return false;
    }
  }

  return true;
};
