"use client";

import React, { useState } from 'react';
import { Card, CardHeader, CardT<PERSON>le, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { FileText, Download, Calendar, Users, Target, CircleDot } from 'lucide-react';
import MainLayout from '@/components/layout/MainLayout';

interface ReportTemplate {
  id: string;
  name: string;
  description: string;
  type: 'player' | 'team' | 'facility';
  icon: React.ComponentType<any>;
}

const reportTemplates: ReportTemplate[] = [
  {
    id: 'player-performance',
    name: 'Player Performance Report',
    description: 'Comprehensive individual player analysis with trends and recommendations',
    type: 'player',
    icon: Target
  },
  {
    id: 'team-summary',
    name: 'Team Summary Report',
    description: 'Team-wide performance metrics and player comparisons',
    type: 'team',
    icon: Users
  },
  {
    id: 'facility-overview',
    name: 'Facility Overview Report',
    description: 'High-level facility metrics and coaching effectiveness',
    type: 'facility',
    icon: CircleDot
  },
  {
    id: 'progress-tracking',
    name: 'Progress Tracking Report',
    description: 'Player development over time with milestone tracking',
    type: 'player',
    icon: Calendar
  }
];

export default function Reports() {
  const [selectedTemplate, setSelectedTemplate] = useState<string>('');
  const [reportTitle, setReportTitle] = useState<string>('');
  const [dateRange, setDateRange] = useState<string>('30');
  const [selectedPlayers, setSelectedPlayers] = useState<string[]>([]);
  const [isGenerating, setIsGenerating] = useState<boolean>(false);

  const generateReport = async () => {
    if (!selectedTemplate) return;
    
    setIsGenerating(true);
    
    // Simulate report generation
    setTimeout(() => {
      setIsGenerating(false);
      // In a real implementation, this would generate and download the report
      alert('Report generated successfully! (This is a demo)');
    }, 2000);
  };

  const getTemplateById = (id: string) => {
    return reportTemplates.find(template => template.id === id);
  };

  return (
    <MainLayout>
      <div className="p-6 space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-white">Reports</h1>
            <p className="text-gray-400 mt-2">Generate customizable reports and analytics</p>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Report Templates */}
          <div className="lg:col-span-2">
            <Card className="floating-card">
              <CardHeader>
                <CardTitle className="text-white">Report Templates</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {reportTemplates.map(template => {
                    const IconComponent = template.icon;
                    return (
                      <div
                        key={template.id}
                        className={`p-4 border rounded-lg cursor-pointer transition-all ${
                          selectedTemplate === template.id
                            ? 'border-orange-500 bg-orange-500/10'
                            : 'border-gray-600 hover:border-gray-500'
                        }`}
                        onClick={() => setSelectedTemplate(template.id)}
                      >
                        <div className="flex items-start space-x-3">
                          <IconComponent className="h-6 w-6 text-orange-500 mt-1" />
                          <div className="flex-1">
                            <h3 className="font-semibold text-white">{template.name}</h3>
                            <p className="text-sm text-gray-400 mt-1">{template.description}</p>
                            <div className="mt-2">
                              <span className={`text-xs px-2 py-1 rounded-full ${
                                template.type === 'player' ? 'bg-blue-500/20 text-blue-400' :
                                template.type === 'team' ? 'bg-green-500/20 text-green-400' :
                                'bg-purple-500/20 text-purple-400'
                              }`}>
                                {template.type}
                              </span>
                            </div>
                          </div>
                        </div>
                      </div>
                    );
                  })}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Report Configuration */}
          <div>
            <Card className="floating-card">
              <CardHeader>
                <CardTitle className="text-white">Report Configuration</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <Label htmlFor="report-title" className="text-white">Report Title</Label>
                  <Input
                    id="report-title"
                    value={reportTitle}
                    onChange={(e) => setReportTitle(e.target.value)}
                    placeholder="Enter report title"
                    className="mt-1 bg-gray-800 border-gray-600 text-white"
                  />
                </div>

                <div>
                  <Label htmlFor="date-range" className="text-white">Date Range</Label>
                  <Select value={dateRange} onValueChange={setDateRange}>
                    <SelectTrigger className="mt-1 bg-gray-800 border-gray-600 text-white">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent className="bg-gray-800 border-gray-600">
                      <SelectItem value="7">Last 7 Days</SelectItem>
                      <SelectItem value="30">Last 30 Days</SelectItem>
                      <SelectItem value="90">Last 90 Days</SelectItem>
                      <SelectItem value="365">Last Year</SelectItem>
                      <SelectItem value="all">All Time</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                {selectedTemplate && getTemplateById(selectedTemplate)?.type === 'player' && (
                  <div>
                    <Label htmlFor="players" className="text-white">Select Players</Label>
                    <Select>
                      <SelectTrigger className="mt-1 bg-gray-800 border-gray-600 text-white">
                        <SelectValue placeholder="Choose players" />
                      </SelectTrigger>
                      <SelectContent className="bg-gray-800 border-gray-600">
                        <SelectItem value="all">All Players</SelectItem>
                        <SelectItem value="john-smith">John Smith</SelectItem>
                        <SelectItem value="mike-johnson">Mike Johnson</SelectItem>
                        <SelectItem value="sarah-davis">Sarah Davis</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                )}

                <div className="pt-4">
                  <Button
                    onClick={generateReport}
                    disabled={!selectedTemplate || isGenerating}
                    className="w-full btn-primary"
                  >
                    {isGenerating ? (
                      <>
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                        Generating...
                      </>
                    ) : (
                      <>
                        <Download className="h-4 w-4 mr-2" />
                        Generate Report
                      </>
                    )}
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Recent Reports */}
        <Card className="floating-card">
          <CardHeader>
            <CardTitle className="text-white">Recent Reports</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {[
                {
                  name: 'Team Performance Summary - June 2024',
                  type: 'Team Summary Report',
                  date: '2024-06-30',
                  size: '2.4 MB'
                },
                {
                  name: 'John Smith Progress Report',
                  type: 'Player Performance Report',
                  date: '2024-06-28',
                  size: '1.8 MB'
                },
                {
                  name: 'Facility Overview - Q2 2024',
                  type: 'Facility Overview Report',
                  date: '2024-06-25',
                  size: '3.1 MB'
                }
              ].map((report, index) => (
                <div key={index} className="flex items-center justify-between p-3 bg-gray-800 rounded-lg">
                  <div className="flex items-center space-x-3">
                    <FileText className="h-5 w-5 text-orange-500" />
                    <div>
                      <p className="font-medium text-white">{report.name}</p>
                      <p className="text-sm text-gray-400">{report.type} • {report.date}</p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    <span className="text-sm text-gray-400">{report.size}</span>
                    <Button size="sm" variant="outline" className="border-orange-500 text-orange-500 hover:bg-orange-500 hover:text-white">
                      <Download className="h-3 w-3" />
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Report Features */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <Card className="floating-card">
            <CardContent className="p-6 text-center">
              <Target className="h-12 w-12 text-orange-500 mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-white mb-2">Customizable Metrics</h3>
              <p className="text-gray-400 text-sm">Choose which metrics to include in your reports</p>
            </CardContent>
          </Card>

          <Card className="floating-card">
            <CardContent className="p-6 text-center">
              <Calendar className="h-12 w-12 text-orange-500 mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-white mb-2">Scheduled Reports</h3>
              <p className="text-gray-400 text-sm">Automatically generate reports on a schedule</p>
            </CardContent>
          </Card>

          <Card className="floating-card">
            <CardContent className="p-6 text-center">
              <Users className="h-12 w-12 text-orange-500 mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-white mb-2">Team Sharing</h3>
              <p className="text-gray-400 text-sm">Share reports with coaches and players</p>
            </CardContent>
          </Card>
        </div>
      </div>
    </MainLayout>
  );
}
