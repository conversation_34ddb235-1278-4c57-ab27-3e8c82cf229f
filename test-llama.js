// Simple test script to verify LLaMA integration
const testLLaMAIntegration = async () => {
  const testData = {
    playerId: 'test-player',
    question: 'Tell me about baseball performance analysis.',
    dataType: 'hitting',
    level: 'Professional',
    hittingData: [
      {
        Batter: 'test-player',
        BatterSide: 'R',
        ExitSpeed: 95.2,
        RelSpeed: 88.5,
        Angle: 15.3,
        Direction: 12.5,
        Distance: 320,
        HitSpinRate: 2200,
        ContactPositionY: 0.5,
        HitSpinAxis: 180,
        Level: 'Professional'
      },
      {
        Batter: 'test-player',
        BatterSide: 'R',
        ExitSpeed: 89.8,
        RelSpeed: 91.2,
        Angle: 22.1,
        Direction: -8.3,
        Distance: 285,
        HitSpinRate: 2450,
        ContactPositionY: 0.3,
        HitSpinAxis: 165,
        Level: 'Professional'
      }
    ],
    pitchingData: []
  };

  try {
    console.log('Testing LLaMA API integration...');
    
    const response = await fetch('http://localhost:3000/api/query', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(testData),
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result = await response.json();
    console.log('✅ API Response received:');
    console.log(JSON.stringify(result, null, 2));
    
    if (result.text) {
      console.log('✅ LLaMA integration working!');
      console.log('Response text:', result.text);
      
      if (result.chart) {
        console.log('✅ Chart data included:', result.chart);
      }
    } else {
      console.log('❌ No text response received');
    }

  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
};

// Run the test
testLLaMAIntegration();
