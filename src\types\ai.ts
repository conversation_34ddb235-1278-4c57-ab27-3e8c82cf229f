/**
 * TypeScript interfaces for AI/LLaMA integration
 */

// LLaMA API Request/Response types
export interface LLaMARequest {
  prompt: string;
  max_tokens?: number;
  temperature?: number;
  top_p?: number;
  stop?: string[];
}

export interface LLaMAResponse {
  content: string;
  model?: string;
  stop_reason?: string;
  tokens_predicted?: number;
  tokens_evaluated?: number;
  generation_settings?: any;
}

// AI Response types for our application
export interface AIResponse {
  text: string;
  chart?: ChartData;
  error?: string;
}

export interface ChartData {
  type: 'bar' | 'line' | 'scatter' | 'pie';
  data: number[];
  labels: string[];
  title?: string;
  xAxisLabel?: string;
  yAxisLabel?: string;
}

// Query types
export interface QueryRequest {
  playerId: string;
  question: string;
  dataType?: 'hitting' | 'pitching' | 'both';
}

// Player metrics types for AI context
export interface PlayerMetrics {
  playerId: string;
  playerName: string;
  level: 'Professional' | 'College' | 'High School';
  dataType: 'hitting' | 'pitching' | 'both';
  
  // Hitting metrics (if applicable)
  hitting?: {
    totalHits: number;
    avgExitVelocity: number;
    avgLaunchAngle: number;
    avgDistance: number;
    qualityOfContactScore: number;
    launchAngleScore: number;
    roomForErrorScore: number;
    totalScore: number;
    hitsByVelocityRange: Array<{
      range: string;
      count: number;
      avgExitVelo: number;
    }>;
  };
  
  // Pitching metrics (if applicable)
  pitching?: {
    totalPitches: number;
    arsenalScore: number;
    pitchTypes: Array<{
      type: string;
      count: number;
      avgVelocity: number;
      avgSpinRate: number;
      avgVertBreak: number;
      avgHorzBreak: number;
      score: number;
    }>;
    velocityByPitchType: Array<{
      pitchType: string;
      avgVelocity: number;
      maxVelocity: number;
      minVelocity: number;
    }>;
  };
}

// Message types for chat interface
export interface ChatMessage {
  id: string;
  role: 'user' | 'assistant';
  content: string;
  timestamp: Date;
  chart?: ChartData;
  isLoading?: boolean;
}

// Prompt template types
export interface PromptTemplate {
  system: string;
  userPrefix: string;
  dataContext: string;
  questionPrefix: string;
}
