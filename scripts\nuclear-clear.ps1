# Nuclear cache clearing script for Windows
# This script aggressively clears all Next.js caches and kills processes

Write-Host "🚀 BLP Nuclear Cache Clearing Script" -ForegroundColor Cyan
Write-Host "=====================================" -ForegroundColor Cyan
Write-Host ""

# Step 1: Kill all Node.js processes
Write-Host "🔪 Step 1: Killing all Node.js processes..." -ForegroundColor Yellow
try {
    Get-Process -Name "node" -ErrorAction SilentlyContinue | Stop-Process -Force -ErrorAction SilentlyContinue
    Write-Host "✅ Node.js processes killed" -ForegroundColor Green
} catch {
    Write-Host "⚠️  No Node.js processes found or couldn't kill them" -ForegroundColor Yellow
}

# Step 2: Kill processes using port 3000
Write-Host "🔪 Step 2: Freeing port 3000..." -ForegroundColor Yellow
try {
    $port3000 = netstat -ano | findstr :3000
    if ($port3000) {
        $pids = $port3000 | ForEach-Object { ($_ -split '\s+')[-1] } | Sort-Object -Unique
        foreach ($pid in $pids) {
            if ($pid -match '^\d+$') {
                Stop-Process -Id $pid -Force -ErrorAction SilentlyContinue
            }
        }
        Write-Host "✅ Port 3000 freed" -ForegroundColor Green
    } else {
        Write-Host "ℹ️  Port 3000 is already free" -ForegroundColor Blue
    }
} catch {
    Write-Host "⚠️  Could not check/free port 3000" -ForegroundColor Yellow
}

# Step 3: Wait for processes to fully terminate
Write-Host "⏳ Step 3: Waiting for processes to terminate..." -ForegroundColor Yellow
Start-Sleep -Seconds 2
Write-Host "✅ Wait completed" -ForegroundColor Green

# Step 4: Nuclear .next directory removal
Write-Host "💥 Step 4: Nuclear .next directory removal..." -ForegroundColor Yellow

if (Test-Path ".next") {
    Write-Host "🗑️  Found .next directory, removing..." -ForegroundColor Yellow
    
    # Method 1: Take ownership
    try {
        Write-Host "   Attempting ownership takeover..." -ForegroundColor Gray
        takeown /f ".next" /r /d y 2>$null | Out-Null
        icacls ".next" /grant administrators:F /t 2>$null | Out-Null
        Write-Host "   ✅ Ownership taken" -ForegroundColor Green
    } catch {
        Write-Host "   ⚠️  Ownership takeover failed" -ForegroundColor Yellow
    }
    
    # Method 2: Recursive force delete
    try {
        Write-Host "   Attempting recursive force delete..." -ForegroundColor Gray
        Remove-Item -Path ".next" -Recurse -Force -ErrorAction Stop
        Write-Host "   ✅ Recursive delete successful" -ForegroundColor Green
    } catch {
        Write-Host "   ⚠️  Recursive delete failed, trying file-by-file..." -ForegroundColor Yellow
        
        # Method 3: File by file
        try {
            Get-ChildItem -Path ".next" -Recurse -Force | ForEach-Object {
                try {
                    Remove-Item $_.FullName -Force -ErrorAction SilentlyContinue
                } catch {
                    # Ignore individual file errors
                }
            }
            Remove-Item -Path ".next" -Force -ErrorAction SilentlyContinue
            Write-Host "   ✅ File-by-file deletion completed" -ForegroundColor Green
        } catch {
            Write-Host "   ❌ File-by-file deletion failed" -ForegroundColor Red
        }
    }
    
    # Verify removal
    if (Test-Path ".next") {
        Write-Host "⚠️  .next directory still exists (some files may be locked)" -ForegroundColor Yellow
        Write-Host "💡 Try closing your IDE/editor and running this script again" -ForegroundColor Blue
    } else {
        Write-Host "✅ .next directory completely removed" -ForegroundColor Green
    }
} else {
    Write-Host "ℹ️  .next directory does not exist" -ForegroundColor Blue
}

# Step 5: Clear npm cache
Write-Host "🧹 Step 5: Clearing npm cache..." -ForegroundColor Yellow
try {
    & npm cache clean --force 2>$null
    Write-Host "✅ npm cache cleared" -ForegroundColor Green
} catch {
    Write-Host "⚠️  Could not clear npm cache" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "🎉 Nuclear cache clearing completed!" -ForegroundColor Green
Write-Host "💡 Now run 'npm run dev' to start fresh" -ForegroundColor Blue
Write-Host "💡 If issues persist, restart your IDE/editor" -ForegroundColor Blue
Write-Host ""
