import React, { useState, useMemo } from 'react';
import {
  <PERSON><PERSON><PERSON>,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  <PERSON>lt<PERSON>,
  Legend,
  ResponsiveContainer,
  Brush,
  ReferenceLine,
} from 'recharts';
import { Checkbox } from "@/components/ui/checkbox";
import { Label as UiLabel } from "@/components/ui/label";
import { Button } from "@/components/ui/button";

type Level = 'Professional' | 'College' | 'High School';

interface PitchMetricsAnalysisProps {
  data: Array<{
    Pitcher: string;
    AutoPitchType: string;
    RelSpeed: number;
    SpinRate: number;
    VertBreak: number;
    HorzBreak: number;
    Level: Level;
    EventID?: string;
    EventDate?: string;
  }>;
  selectedPitcher: string;
  selectedLevel: Level;
}

// Metric configuration constant
const metricConfigs = [
  { id: 'velocity', name: 'Velocity', key: 'RelSpeed' },
  { id: 'spinRate', name: 'Spin Rate', key: 'SpinRate' },
  { id: 'vertBreak', name: 'Vertical Break', key: 'VertBreak' },
  { id: 'horzBreak', name: 'Horizontal Break', key: 'HorzBreak' },
];

//
// Helper: Simple linear regression function using points { x, y }.
// Returns { slope, intercept }
const linearRegression = (dataPoints: { x: number; y: number }[]) => {
  const n = dataPoints.length;
  if (n < 2) return { slope: 0, intercept: 0 };
  const sumX = dataPoints.reduce((sum, p) => sum + p.x, 0);
  const sumY = dataPoints.reduce((sum, p) => sum + p.y, 0);
  const sumXY = dataPoints.reduce((sum, p) => sum + p.x * p.y, 0);
  const sumXX = dataPoints.reduce((sum, p) => sum + p.x * p.x, 0);
  const slope = (n * sumXY - sumX * sumY) / (n * sumXX - sumX * sumX);
  const intercept = (sumY - slope * sumX) / n;
  return { slope, intercept };
};

//
// Helper: Given an array of data objects, group them by the day (using baseTimestamp) and return the groups.
const groupByDate = (data: any[]) => {
  const groups: Record<string, any[]> = {};
  data.forEach((d) => {
    // d.baseTimestamp is the date’s midnight
    const key = d.baseTimestamp.toString();
    if (!groups[key]) groups[key] = [];
    groups[key].push(d);
  });
  return groups;
};

//
// The main component.
const PitchMetricsAnalysis: React.FC<PitchMetricsAnalysisProps> = ({
  data,
  selectedPitcher,
  selectedLevel,
}) => {
  // State for which pitch types to display.
  const [selectedPitches, setSelectedPitches] = useState<string[]>([]);
  // State for which metrics to display.
  const [selectedMetrics, setSelectedMetrics] = useState<string[]>(['velocity']);
  // State to toggle trend line display.
  const [showTrend, setShowTrend] = useState<boolean>(false);
  // State for brush zoom: we keep the current visible data indices.
  const [brushIndices, setBrushIndices] = useState<{ startIndex: number; endIndex: number } | null>(null);

  // First, filter & augment the raw data.
  // We add:
  //  • timestamp: the numeric value of EventDate.
  //  • baseTimestamp: the midnight (date only) for EventDate (for trend regression).
  const augmentedData = useMemo(() => {
    return data
      .filter((d) => d.EventDate) // only use events with a date
      .map((d) => {
        const eventDate = new Date(d.EventDate!);
        return {
          ...d,
          timestamp: eventDate.getTime(),
          baseTimestamp: new Date(eventDate.getFullYear(), eventDate.getMonth(), eventDate.getDate()).getTime(),
        };
      });
  }, [data]);

  // Filter data for the selected pitcher and pitch types.
  const filteredData = useMemo(() => {
    return augmentedData
      .filter(
        (pitch) =>
          pitch.Pitcher === selectedPitcher &&
          selectedPitches.includes(pitch.AutoPitchType)
      )
      .sort((a, b) => a.timestamp - b.timestamp);
  }, [augmentedData, selectedPitches, selectedPitcher]);

  // For plotting raw events without stacking,
  // we “jitter” the x value for events that occur on the same day.
  // For each day, we spread events out by adding a small offset.
  const plotData = useMemo(() => {
    // First, group by day (using baseTimestamp)
    const groups = groupByDate(filteredData);
    const result: any[] = [];
    // For each group, compute offsets.
    // We'll add/subtract a constant offset (e.g. 5 minutes in ms) so that if there are multiple events, they are slightly separated.
    const offsetIncrement = 5 * 60 * 1000; // 5 minutes
    for (const key in groups) {
      const group = groups[key];
      const n = group.length;
      // Sort group by original timestamp (if not already sorted)
      group.sort((a, b) => a.timestamp - b.timestamp);
      group.forEach((item, index) => {
        // Center the offsets around 0.
        const offset = (index - (n - 1) / 2) * offsetIncrement;
        // The plotTimestamp is the baseTimestamp plus offset.
        result.push({ ...item, plotTimestamp: item.baseTimestamp + offset });
      });
    }
    // Sort the overall result by plotTimestamp.
    return result.sort((a, b) => a.plotTimestamp - b.plotTimestamp);
  }, [filteredData]);

  // The visible data depends on the Brush.
  // If no brush is active, use all plotData.
  const visibleData = useMemo(() => {
    if (brushIndices && plotData.length > 0) {
      return plotData.slice(brushIndices.startIndex, brushIndices.endIndex + 1);
    }
    return plotData;
  }, [brushIndices, plotData]);

  // Get unique pitch types for the selected pitcher (for checkboxes).
  const pitchTypes = useMemo(() => {
    return [
      ...new Set(
        data
          .filter((pitch) => pitch.Pitcher === selectedPitcher)
          .map((pitch) => pitch.AutoPitchType)
      ),
    ];
  }, [data, selectedPitcher]);

  // Helper: determine the color for a given pitch type.
  const getLineColor = (pitchType: string, metricId: string): string => {
    const pitchColors: { [key: string]: string } = {
      FF: '#e63946',
      SL: '#1d3557',
      CH: '#2a9d8f',
      CU: '#6b705c',
      Sinker: '#f4a261',
      Sweeper: '#bc6c25',
      Splitter: '#588157',
    };
    return pitchColors[pitchType] || '#000000';
  };

  // Use the first selected metric (or default to "velocity") for trend line calculations.
  const trendMetricId = selectedMetrics[0] || 'velocity';
  const trendMetricConfig = metricConfigs.find((m) => m.id === trendMetricId);

  // For the trend line, we compute regression on the visible raw data.
  // To avoid the jitter affecting the regression, we use the baseTimestamp for x.
  // We compute one trend line for the "active" pitch type—that is, the first selected pitch type.
  const activePitchType = selectedPitches[0] || null;
  const trendResult = useMemo(() => {
    if (!trendMetricConfig || !activePitchType) return null;
    // Filter visibleData for the active pitch type.
    const dataForTrend = visibleData.filter((d) => d.AutoPitchType === activePitchType);
    if (dataForTrend.length < 2) return null;
    // Use each event's baseTimestamp as x and the chosen metric as y.
    const dataPoints = dataForTrend.map((d) => ({
      x: d.baseTimestamp,
      y: d[trendMetricConfig.key],
    }));
    const { slope, intercept } = linearRegression(dataPoints);
    // Determine the visible x range for the active pitch type.
    const xValues = dataPoints.map((pt) => pt.x);
    const xMin = Math.min(...xValues);
    const xMax = Math.max(...xValues);
    // Compute predicted y values at the endpoints.
    const yMin = slope * xMin + intercept;
    const yMax = slope * xMax + intercept;
    // Calculate percentage change based on the trend line.
    const percentageChange = ((yMax - yMin) / yMin) * 100;
    return {
      slope,
      intercept,
      // For drawing the trend line we use the base timestamps for x,
      // then later convert them to the corresponding plot positions (which for a given day is just the base value).
      linePoints: [
        { x: xMin, y: yMin },
        { x: xMax, y: yMax },
      ],
      percentageChange,
    };
  }, [trendMetricConfig, activePitchType, visibleData]);

  // When drawing the trend line, we want to map the x values (which are baseTimestamps)
  // to the x-axis positions. Since our x-axis is keyed to plotTimestamp, and the jitter is centered around baseTimestamp,
  // we can use baseTimestamp directly.
  // We then form an array for the trend line.
  const trendLineData = useMemo(() => {
    if (!trendResult) return [];
    return trendResult.linePoints.map((pt) => ({
      plotTimestamp: pt.x, // use the base (central) time for that day
      trend: pt.y,
    }));
  }, [trendResult]);

  return (
    <div className="space-y-4">
      {/* Controls */}
      <div className="flex flex-wrap gap-8 items-center">
        <div>
          <h4 className="mb-2 text-sm font-medium">Pitch Types</h4>
          <div className="space-y-2">
            {pitchTypes.map((type) => (
              <div key={type} className="flex items-center space-x-2">
                <Checkbox
                  id={`pitch-${type}`}
                  checked={selectedPitches.includes(type)}
                  onCheckedChange={(checked) => {
                    if (checked) {
                      setSelectedPitches([...selectedPitches, type]);
                    } else {
                      setSelectedPitches(selectedPitches.filter((p) => p !== type));
                    }
                    // Reset zoom on change.
                    setBrushIndices(null);
                  }}
                />
                <UiLabel htmlFor={`pitch-${type}`}>{type}</UiLabel>
              </div>
            ))}
          </div>
        </div>

        <div>
          <h4 className="mb-2 text-sm font-medium">Metrics</h4>
          <div className="space-y-2">
            {metricConfigs.map((metric) => (
              <div key={metric.id} className="flex items-center space-x-2">
                <Checkbox
                  id={`metric-${metric.id}`}
                  checked={selectedMetrics.includes(metric.id)}
                  onCheckedChange={(checked) => {
                    if (checked) {
                      setSelectedMetrics([...selectedMetrics, metric.id]);
                    } else {
                      setSelectedMetrics(selectedMetrics.filter((m) => m !== metric.id));
                    }
                    // Reset zoom on change.
                    setBrushIndices(null);
                  }}
                />
                <UiLabel htmlFor={`metric-${metric.id}`}>{metric.name}</UiLabel>
              </div>
            ))}
          </div>
        </div>

        <Button
          onClick={() => setBrushIndices(null)}
          variant="outline"
          className="bg-[rgba(15,15,35,0.8)] text-white border border-[#e55a2b] shadow-lg shadow-[#e55a2b]/20 hover:shadow-[#e55a2b]/40 transition-all duration-300"
        >
          Reset Zoom
        </Button>

        <Button
          onClick={() => setShowTrend(!showTrend)}
          variant="outline"
          className="bg-[rgba(15,15,35,0.8)] text-white border border-[#e55a2b] shadow-lg shadow-[#e55a2b]/20 hover:shadow-[#e55a2b]/40 transition-all duration-300 flex items-center gap-2"
        >
          {showTrend ? 'Hide Trend' : 'Show Trend'}
          {trendResult && (
            <span className={trendResult.percentageChange >= 0 ? 'text-green-400' : 'text-red-400'}>
              {trendResult.percentageChange >= 0
                ? `+${trendResult.percentageChange.toFixed(1)}%`
                : `${trendResult.percentageChange.toFixed(1)}%`}
            </span>
          )}
        </Button>
      </div>

      {/* Chart */}
      <div className="h-[400px]">
        <ResponsiveContainer>
          <LineChart
            data={plotData} // use the jittered data for plotting
            margin={{ top: 20, right: 30, left: 40, bottom: 20 }}
          >
            <CartesianGrid strokeDasharray="3 3" stroke="#ffffff" />
            {/* XAxis: using plotTimestamp (numeric) */}
            <XAxis
              dataKey="plotTimestamp"
              type="number"
              domain={['auto', 'auto']}
              tickFormatter={(tick) => new Date(tick).toLocaleDateString()}
              label={{
                value: 'Event Date',
                position: 'bottom',
                style: { fontWeight: 'bold', fill: '#ffffff' },
              }}
              padding={{ left: 20, right: 20 }}
              stroke="#ffffff"
            />
            <YAxis
              type="number"
              domain={['auto', 'auto']}
              padding={{ top: 20, bottom: 20 }}
              label={{
                value: 'Value',
                angle: -90,
                position: 'insideLeft',
                offset: -10,
                style: { fontWeight: 'bold', fill: '#ffffff' },
              }}
              stroke="#ffffff"
            />
            <Tooltip
              formatter={(value: number, name: string) => [value.toFixed(2), name]}
              labelFormatter={(label) => new Date(label).toLocaleDateString()}
              contentStyle={{
                backgroundColor: 'rgba(15, 15, 35, 0.9)',
                border: '1px solid #e55a2b',
                borderRadius: '8px',
                color: '#ffffff'
              }}
            />
            <Legend />

            {/* Render a Line for each selected pitch type */}
            {selectedPitches.map((pitchType) =>
              selectedMetrics.map((metricId) => {
                const metric = metricConfigs.find((m) => m.id === metricId);
                if (!metric) return null;
                // Filter plotData for this pitch type.
                const lineData = plotData.filter((p) => p.AutoPitchType === pitchType);
                return (
                  <Line
                    key={`${pitchType}-${metricId}`}
                    type="monotone"
                    data={lineData}
                    dataKey={metric.key}
                    name={`${pitchType} ${metric.name}`}
                    stroke={getLineColor(pitchType, metricId)}
                    dot={{ r: 3 }}
                    activeDot={{ r: 5 }}
                    connectNulls
                  />
                );
              })
            )}

            {/* Brush for zooming.
                When the brush changes, update brushIndices so visibleData (and trend line) update.
            */}
            <Brush
              dataKey="plotTimestamp"
              height={40}
              stroke="#e55a2b"
              tickFormatter={(tick) => new Date(tick).toLocaleDateString()}
              onChange={(brushData) => {
                if (
                  brushData &&
                  typeof brushData.startIndex === 'number' &&
                  typeof brushData.endIndex === 'number'
                ) {
                  setBrushIndices({ startIndex: brushData.startIndex, endIndex: brushData.endIndex });
                } else {
                  setBrushIndices(null);
                }
              }}
            />

            {/* Optionally, show ReferenceLines for each event.
                (They display the event number and help identify individual events.)
            */}
            {plotData.map((entry, idx) => (
              <ReferenceLine
                key={`event-${idx}`}
                x={entry.plotTimestamp}
                stroke="#e55a2b"
                strokeDasharray="3 3"
                label={{
                  value: `E${idx + 1}`,
                  position: 'top',
                  style: { fontSize: 10, fill: '#e55a2b' },
                }}
              />
            ))}

            {/* Render trend line (only for the active pitch type) if toggled on */}
            {showTrend && trendLineData.length === 2 && (
              <Line
                type="monotone"
                data={trendLineData}
                dataKey="trend"
                name="Trend Line"
                stroke="#ffffff"
                strokeDasharray="5 5"
                dot={false}
              />
            )}
          </LineChart>
        </ResponsiveContainer>
      </div>
    </div>
  );
};

export default PitchMetricsAnalysis;
