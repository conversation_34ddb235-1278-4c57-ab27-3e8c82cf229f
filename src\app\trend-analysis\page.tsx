"use client"

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { ArrowLeft } from 'lucide-react';
import HittingTrendAnalysis from '@/components/dashboard/Hitting/HittingTrendAnalysis';
import { useSearchParams } from 'next/navigation';
import AdvancedMetricsPanel from '@/components/dashboard/Hitting/AdvancedMetricsPanel';

export default function TrendAnalysis() {
  const router = useRouter();
  const [selectedMetric, setSelectedMetric] = useState<string>('overall');
  const [timeRange, setTimeRange] = useState<string>('30');
  const searchParams = useSearchParams();
const [selectedLevel, setSelectedLevel] = useState<'Professional' | 'College' | 'High School'>('College');
const [selectedBatter, setSelectedBatter] = useState<string | null>(null);
const [data, setData] = useState<any[]>([]);

// Get data from localStorage on component mount
React.useEffect(() => {
    const { getStorageData, validateHittingData, STORAGE_KEYS } = require('@/lib/storage');

    const storedData = getStorageData(STORAGE_KEYS.HITTING_DATA, []);
    const storedBatter = getStorageData(STORAGE_KEYS.SELECTED_BATTER, '');
    const storedLevel = getStorageData(STORAGE_KEYS.SELECTED_LEVEL, 'Professional');

    console.log('Retrieved data:', storedData);
    console.log('Selected batter in trend:', storedBatter);
    console.log('Selected level in trend:', storedLevel);

    // Validate data before using it
    if (validateHittingData(storedData)) {
      setData(storedData);
    } else {
      console.warn('Invalid hitting data found, clearing...');
      setData([]);
    }

    if (storedBatter) {
      setSelectedBatter(storedBatter);
    }
    if (storedLevel) {
      setSelectedLevel(storedLevel as 'Professional' | 'College' | 'High School');
    }
  }, []);


  return (
    <div className="min-h-screen w-full p-6" style={{ backgroundColor: '#f6e1bd' }}>
      <div className="max-w-7xl mx-auto space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
        <Button 
            onClick={() => {
                // Keep all data in localStorage and specify hitting view
                router.push(`/?view=hitting&selectedBatter=${encodeURIComponent(selectedBatter || '')}&selectedLevel=${selectedLevel}`);
            }}
            variant="outline"
            className="bg-white/80 text-black border-black/20 hover:bg-white/90"
            >
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back to Dashboard
            </Button>
          
          <div className="flex gap-4">
            <Select value={selectedMetric} onValueChange={setSelectedMetric}>
              <SelectTrigger className="w-[200px] bg-white/80 text-black border-black/20">
                <SelectValue placeholder="Select Metric" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="overall">Overall Score</SelectItem>
                <SelectItem value="qualityOfContact">Quality of Contact</SelectItem>
                <SelectItem value="roomForError">Room for Error</SelectItem>
                <SelectItem value="launchAngle">Launch Angle</SelectItem>
              </SelectContent>
            </Select>

            <Select value={timeRange} onValueChange={setTimeRange}>
              <SelectTrigger className="w-[200px] bg-white/80 text-black border-black/20">
                <SelectValue placeholder="Select Time Range" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="7">Last 7 Days</SelectItem>
                <SelectItem value="30">Last 30 Days</SelectItem>
                <SelectItem value="90">Last 90 Days</SelectItem>
                <SelectItem value="all">All Time</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>
            <div className="text-center mb-6">
            <h1 className="text-3xl font-bold text-black">
                {selectedBatter ? `${selectedBatter}'s Performance Trends` : 'Performance Trends'}
            </h1>
            </div>

        {/* Main Content */}
        <Card className="bg-white backdrop-blur border border-black shadow-lg">
          <CardHeader>
            <CardTitle className="text-black">Hitting Trend Analysis</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="h-[600px]">
            {data.length > 0 && selectedBatter && (
                    <HittingTrendAnalysis 
                        data={data.map(hit => ({ ...hit, Level: selectedLevel }))}  // Add the level to each hit
                        selectedBatter={selectedBatter}
                        selectedLevel={selectedLevel}
                    />
                    )}
            </div>
            </CardContent>
        </Card>
            <AdvancedMetricsPanel 
        data={data}
        selectedBatter={selectedBatter || ''}
    />
    </div>
    </div>
  );
}