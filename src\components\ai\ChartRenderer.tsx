"use client";

import React from 'react';
import {
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON>hart,
  Line,
  <PERSON>att<PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  Pie,
  Cell,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer
} from 'recharts';
import { ChartData } from '@/types/ai';

interface ChartRendererProps {
  chart: ChartData;
  className?: string;
}

export default function ChartRenderer({ chart, className = '' }: ChartRendererProps) {
  // Prepare data for recharts
  const chartData = chart.labels.map((label, index) => ({
    name: label,
    value: chart.data[index] || 0,
    x: chart.data[index] || 0,
    y: index // For scatter plots
  }));

  // Colors for charts (matching your orange theme)
  const colors = [
    '#f97316', // orange-500
    '#ea580c', // orange-600
    '#dc2626', // red-600
    '#16a34a', // green-600
    '#2563eb', // blue-600
    '#7c3aed', // violet-600
    '#db2777', // pink-600
    '#059669'  // emerald-600
  ];

  const renderChart = () => {
    switch (chart.type) {
      case 'bar':
        return (
          <ResponsiveContainer width="100%" height={200}>
            <BarChart data={chartData} margin={{ top: 5, right: 5, left: 5, bottom: 5 }}>
              <CartesianGrid strokeDasharray="3 3" stroke="#e5e7eb" />
              <XAxis 
                dataKey="name" 
                tick={{ fontSize: 10 }}
                stroke="#6b7280"
              />
              <YAxis 
                tick={{ fontSize: 10 }}
                stroke="#6b7280"
              />
              <Tooltip 
                contentStyle={{
                  backgroundColor: '#f9fafb',
                  border: '1px solid #e5e7eb',
                  borderRadius: '6px',
                  fontSize: '12px'
                }}
              />
              <Bar 
                dataKey="value" 
                fill={colors[0]}
                radius={[2, 2, 0, 0]}
              />
            </BarChart>
          </ResponsiveContainer>
        );

      case 'line':
        return (
          <ResponsiveContainer width="100%" height={200}>
            <LineChart data={chartData} margin={{ top: 5, right: 5, left: 5, bottom: 5 }}>
              <CartesianGrid strokeDasharray="3 3" stroke="#e5e7eb" />
              <XAxis 
                dataKey="name" 
                tick={{ fontSize: 10 }}
                stroke="#6b7280"
              />
              <YAxis 
                tick={{ fontSize: 10 }}
                stroke="#6b7280"
              />
              <Tooltip 
                contentStyle={{
                  backgroundColor: '#f9fafb',
                  border: '1px solid #e5e7eb',
                  borderRadius: '6px',
                  fontSize: '12px'
                }}
              />
              <Line 
                type="monotone" 
                dataKey="value" 
                stroke={colors[0]}
                strokeWidth={2}
                dot={{ fill: colors[0], strokeWidth: 2, r: 3 }}
                activeDot={{ r: 5, fill: colors[1] }}
              />
            </LineChart>
          </ResponsiveContainer>
        );

      case 'scatter':
        return (
          <ResponsiveContainer width="100%" height={200}>
            <ScatterChart data={chartData} margin={{ top: 5, right: 5, left: 5, bottom: 5 }}>
              <CartesianGrid strokeDasharray="3 3" stroke="#e5e7eb" />
              <XAxis 
                type="number"
                dataKey="x"
                tick={{ fontSize: 10 }}
                stroke="#6b7280"
                name={chart.xAxisLabel || 'X'}
              />
              <YAxis 
                type="number"
                dataKey="y"
                tick={{ fontSize: 10 }}
                stroke="#6b7280"
                name={chart.yAxisLabel || 'Y'}
              />
              <Tooltip 
                cursor={{ strokeDasharray: '3 3' }}
                contentStyle={{
                  backgroundColor: '#f9fafb',
                  border: '1px solid #e5e7eb',
                  borderRadius: '6px',
                  fontSize: '12px'
                }}
              />
              <Scatter 
                dataKey="value" 
                fill={colors[0]}
              />
            </ScatterChart>
          </ResponsiveContainer>
        );

      case 'pie':
        return (
          <ResponsiveContainer width="100%" height={200}>
            <PieChart margin={{ top: 5, right: 5, left: 5, bottom: 5 }}>
              <Pie
                data={chartData}
                cx="50%"
                cy="50%"
                outerRadius={60}
                fill="#8884d8"
                dataKey="value"
                label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                labelStyle={{ fontSize: '10px' }}
              >
                {chartData.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={colors[index % colors.length]} />
                ))}
              </Pie>
              <Tooltip 
                contentStyle={{
                  backgroundColor: '#f9fafb',
                  border: '1px solid #e5e7eb',
                  borderRadius: '6px',
                  fontSize: '12px'
                }}
              />
            </PieChart>
          </ResponsiveContainer>
        );

      default:
        return (
          <div className="flex items-center justify-center h-32 text-gray-500 text-sm">
            Unsupported chart type: {chart.type}
          </div>
        );
    }
  };

  return (
    <div className={`bg-white rounded-lg border border-gray-200 p-3 ${className}`}>
      {chart.title && (
        <h4 className="text-sm font-medium text-gray-900 mb-2 text-center">
          {chart.title}
        </h4>
      )}
      
      {renderChart()}
      
      {(chart.xAxisLabel || chart.yAxisLabel) && (
        <div className="mt-2 text-xs text-gray-500 text-center">
          {chart.xAxisLabel && chart.yAxisLabel && (
            <span>{chart.xAxisLabel} vs {chart.yAxisLabel}</span>
          )}
          {chart.xAxisLabel && !chart.yAxisLabel && (
            <span>{chart.xAxisLabel}</span>
          )}
          {!chart.xAxisLabel && chart.yAxisLabel && (
            <span>{chart.yAxisLabel}</span>
          )}
        </div>
      )}
    </div>
  );
}
