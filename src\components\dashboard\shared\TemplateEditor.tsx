import React, { useState } from "react";
import GridLayout from "react-grid-layout";
import "react-grid-layout/css/styles.css";
import "react-resizable/css/styles.css";
import { Button } from "@/components/ui/button";
import { ScoreGauge } from "@/components/dashboard/PitchingMetricsDashboard";

const defaultLayout = [
    { i: "title", x: 0, y: 0, w: 6, h: 1 },
    { i: "playerName", x: 0, y: 1, w: 6, h: 1 },
    { i: "qualityGauge", x: 0, y: 2, w: 6, h: 2, metric: "Quality of Contact" },
    { i: "launchGauge", x: 0, y: 4, w: 6, h: 2, metric: "Launch Angle" },
    { i: "roomForErrorGauge", x: 0, y: 6, w: 6, h: 2, metric: "Room for Error" },
    { i: "notes", x: 0, y: 8, w: 6, h: 2 }
  ];
  
  const TemplateEditor = ({ onSave, calculatedMetrics }: { 
    onSave: (layout: any) => void;
    calculatedMetrics: Record<string, number>;
  }) => {
    const [layout, setLayout] = useState(defaultLayout);

  const handleSave = () => {
    const { setStorageData, STORAGE_KEYS } = require('@/lib/storage');
    setStorageData(STORAGE_KEYS.REPORT_TEMPLATE, layout);
    onSave(layout);
  };

  return (
    <div className="p-4">
      <h2 className="text-lg font-bold mb-4">Report Layout Editor</h2>
      <GridLayout
        className="layout"
        layout={layout}
        cols={6}
        rowHeight={50}
        width={800}
        onLayoutChange={(newLayout) => setLayout(newLayout)}
      >
        <div key="title" className="border p-2 bg-gray-100 text-center">
          <h3>Report Title</h3>
        </div>
        <div key="playerName" className="border p-2 bg-gray-100 text-center">
          <h3>Player Name</h3>
        </div>
        <div key="qualityGauge" className="border p-2 bg-gray-100 text-center">
            <h3>Quality of Contact</h3>
            <p className="font-bold text-lg">{calculatedMetrics['Quality of Contact'] || 'N/A'}</p>
            </div>

            <div key="qualityGauge" className="border p-2 bg-gray-100 text-center">
  <h3>Slugging Potential</h3>
  <p className="font-bold text-lg">{calculatedMetrics['Launch Angle'] || 'N/A'}</p>
</div>

<div key="qualityGauge" className="border p-2 bg-gray-100 text-center">
  <h3>Room for Error</h3>
  <p className="font-bold text-lg">{calculatedMetrics['Room For Error'] || 'N/A'}</p>
</div>

        <div key="notes" className="border p-2 bg-gray-100 text-center">
          <h3>Notes</h3>
        </div>
      </GridLayout>
      <Button onClick={handleSave} className="mt-4">
        Save Template
      </Button>
    </div>
  );
};

export default TemplateEditor;
