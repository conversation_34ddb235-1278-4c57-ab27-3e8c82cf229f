"use client"

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import { <PERSON>, CardHeader, CardTitle, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { ArrowLeft } from 'lucide-react';
import PitchingTrendAnalysis from '@/components/dashboard/Pitching/PitchingTrendAnalysis';
import PitchMetricsAnalysis from '@/components/dashboard/Pitching/PitchingMetricsAnalysis';

export default function PitchingTrendAnalysisPage() {
  const router = useRouter();
  const [selectedMetric, setSelectedMetric] = useState<string>('overall');
  const [timeRange, setTimeRange] = useState<string>('30');
  const [selectedLevel, setSelectedLevel] = useState<'Professional' | 'College' | 'High School'>('College');
  const [selectedPitcher, setSelectedPitcher] = useState<string | null>(null);
  const [data, setData] = useState<any[]>([]);

  React.useEffect(() => {
    const { getStorageData, validatePitchingData, STORAGE_KEYS } = require('@/lib/storage');

    const storedData = getStorageData(STORAGE_KEYS.PITCHING_DATA, []);
    const storedPitcher = getStorageData(STORAGE_KEYS.SELECTED_PITCHER, '');
    const storedLevel = getStorageData(STORAGE_KEYS.SELECTED_LEVEL, 'Professional');

    console.log('Raw stored data:', storedData);
    console.log('Raw stored pitcher:', storedPitcher);

    // Validate data before using it
    if (validatePitchingData(storedData)) {
        console.log('Parsed data first row:', storedData[0]);
        setData(storedData);
    } else {
        console.warn('Invalid pitching data found, clearing...');
        setData([]);
    }

    if (storedPitcher) {
        console.log('Setting selected pitcher to:', storedPitcher);
        setSelectedPitcher(storedPitcher);
    }
    if (storedLevel) {
        setSelectedLevel(storedLevel as 'Professional' | 'College' | 'High School');
    }
}, []);


  return (
    <div className="min-h-screen w-full p-6" style={{ background: 'linear-gradient(135deg, #0f0f23 0%, #1a1a2e 50%, #16213e 100%)' }}>
      <div className="max-w-7xl mx-auto space-y-6">
        <div className="flex items-center justify-between">
          <Button
            onClick={() => router.push('/?view=pitching')}
            variant="outline"
            className="bg-[rgba(15,15,35,0.8)] text-white border border-[#e55a2b] shadow-lg shadow-[#e55a2b]/20 hover:shadow-[#e55a2b]/40 transition-all duration-300"
          >
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back to Dashboard
          </Button>
        </div>

        <div className="text-center mb-6">
          <h1 className="text-3xl font-bold text-white">
            {selectedPitcher ? `${selectedPitcher}'s Pitching Trends` : 'Pitching Trends'}
          </h1>
        </div>

        <Card className="bg-[rgba(15,15,35,0.8)] backdrop-blur-sm border-0 rounded-lg transform translate-y-0 shadow-[0_4px_16px_rgba(229,90,43,0.15)] hover:shadow-[0_6px_24px_rgba(229,90,43,0.25)] hover:-translate-y-1 transition-all duration-300">
          <CardHeader>
            <CardTitle className="text-white">Pitching Trend Analysis</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="h-[600px]">
              {data.length > 0 && selectedPitcher && (
                <PitchingTrendAnalysis
                  data={data}
                  selectedPitcher={selectedPitcher}
                  selectedLevel={selectedLevel}
                />
              )}
            </div>
          </CardContent>
        </Card>
        <Card className="bg-[rgba(15,15,35,0.8)] backdrop-blur-sm border-0 rounded-lg transform translate-y-0 shadow-[0_4px_16px_rgba(229,90,43,0.15)] hover:shadow-[0_6px_24px_rgba(229,90,43,0.25)] hover:-translate-y-1 transition-all duration-300">
            <CardHeader>
                <CardTitle className="text-white">Pitch Metrics Analysis</CardTitle>
            </CardHeader>
            <CardContent>
                <div className="h-[600px]">
                {data.length > 0 && selectedPitcher && (
                    <PitchMetricsAnalysis
                    data={data}
                    selectedPitcher={selectedPitcher}
                    selectedLevel={selectedLevel}
                    />
                )}
                </div>
            </CardContent>
            </Card>
      </div>
    </div>
  );
}