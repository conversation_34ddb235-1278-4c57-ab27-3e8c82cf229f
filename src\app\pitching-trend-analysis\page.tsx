"use client"

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { ArrowLeft } from 'lucide-react';
import PitchingTrendAnalysis from '@/components/dashboard/Pitching/PitchingTrendAnalysis';
import PitchMetricsAnalysis from '@/components/dashboard/Pitching/PitchingMetricsAnalysis';
import MainLayout from '@/components/layout/MainLayout';

export default function PitchingTrendAnalysisPage() {
  const router = useRouter();
  const [selectedMetric, setSelectedMetric] = useState<string>('overall');
  const [timeRange, setTimeRange] = useState<string>('30');
  const [selectedLevel, setSelectedLevel] = useState<'Professional' | 'College' | 'High School'>('College');
  const [selectedPitcher, setSelectedPitcher] = useState<string | null>(null);
  const [data, setData] = useState<any[]>([]);

  React.useEffect(() => {
    const { getStorageData, validatePitchingData, STORAGE_KEYS } = require('@/lib/storage');

    const storedData = getStorageData(STORAGE_KEYS.PITCHING_DATA, []);
    const storedPitcher = getStorageData(STORAGE_KEYS.SELECTED_PITCHER, '');
    const storedLevel = getStorageData(STORAGE_KEYS.SELECTED_LEVEL, 'Professional');

    console.log('Raw stored data:', storedData);
    console.log('Raw stored pitcher:', storedPitcher);

    // Validate data before using it
    if (validatePitchingData(storedData)) {
        console.log('Parsed data first row:', storedData[0]);
        setData(storedData);
    } else {
        console.warn('Invalid pitching data found, clearing...');
        setData([]);
    }

    if (storedPitcher) {
        console.log('Setting selected pitcher to:', storedPitcher);
        setSelectedPitcher(storedPitcher);
    }
    if (storedLevel) {
        setSelectedLevel(storedLevel as 'Professional' | 'College' | 'High School');
    }
}, []);


  return (
    <MainLayout>
      <div className="p-6 space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-white">
              {selectedPitcher ? `${selectedPitcher}'s Pitching Trends` : 'Pitching Trends'}
            </h1>
            <p className="text-gray-400 mt-2">Advanced pitching trend analysis and metrics</p>
          </div>
          <Button
            onClick={() => router.push('/player-dashboard')}
            variant="outline"
            className="border-orange-500 text-orange-500 hover:bg-orange-500 hover:text-white"
          >
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back to Player Dashboard
          </Button>
        </div>

        <Card className="floating-card">
          <CardHeader>
            <CardTitle className="text-white">Pitching Trend Analysis</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="h-[600px]">
              {data.length > 0 && selectedPitcher && (
                <PitchingTrendAnalysis
                  data={data}
                  selectedPitcher={selectedPitcher}
                  selectedLevel={selectedLevel}
                />
              )}
            </div>
          </CardContent>
        </Card>
        <Card className="floating-card">
            <CardHeader>
                <CardTitle className="text-white">Pitch Metrics Analysis</CardTitle>
            </CardHeader>
            <CardContent>
                <div className="h-[600px]">
                {data.length > 0 && selectedPitcher && (
                    <PitchMetricsAnalysis
                    data={data}
                    selectedPitcher={selectedPitcher}
                    selectedLevel={selectedLevel}
                    />
                )}
                </div>
            </CardContent>
            </Card>
      </div>
    </MainLayout>
  );
}