Write-Host "BLP Nuclear Cache Clearing Script" -ForegroundColor Cyan
Write-Host "=================================" -ForegroundColor Cyan

Write-Host "Killing Node.js processes..." -ForegroundColor Yellow
Get-Process -Name "node" -ErrorAction SilentlyContinue | Stop-Process -Force -ErrorAction SilentlyContinue

Write-Host "Freeing port 3000..." -ForegroundColor Yellow
$port3000 = netstat -ano | findstr :3000
if ($port3000) {
    $pids = $port3000 | ForEach-Object { ($_ -split '\s+')[-1] } | Sort-Object -Unique
    foreach ($pid in $pids) {
        if ($pid -match '^\d+$') {
            Stop-Process -Id $pid -Force -ErrorAction SilentlyContinue
        }
    }
}

Write-Host "Waiting for processes to terminate..." -ForegroundColor Yellow
Start-Sleep -Seconds 2

Write-Host "Removing .next directory..." -ForegroundColor Yellow
if (Test-Path ".next") {
    takeown /f ".next" /r /d y 2>$null | Out-Null
    icacls ".next" /grant administrators:F /t 2>$null | Out-Null
    Remove-Item -Path ".next" -Recurse -Force -ErrorAction SilentlyContinue
    
    if (Test-Path ".next") {
        Get-ChildItem -Path ".next" -Recurse -Force | ForEach-Object {
            Remove-Item $_.FullName -Force -ErrorAction SilentlyContinue
        }
        Remove-Item -Path ".next" -Force -ErrorAction SilentlyContinue
    }
    
    if (Test-Path ".next") {
        Write-Host "Some files may still be locked" -ForegroundColor Yellow
    } else {
        Write-Host ".next directory removed successfully" -ForegroundColor Green
    }
} else {
    Write-Host ".next directory does not exist" -ForegroundColor Blue
}

Write-Host "Cache clearing completed!" -ForegroundColor Green
