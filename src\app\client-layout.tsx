"use client"

import { useEffect } from "react";
import { initializeStorage, clearCacheInDevelopment } from "@/lib/storage";
import CacheDebugPanel from "@/components/debug/CacheDebugPanel";

interface ClientLayoutProps {
  children: React.ReactNode;
}

export default function ClientLayout({ children }: ClientLayoutProps) {
  // Initialize storage on app start
  useEffect(() => {
    initializeStorage();
    clearCacheInDevelopment();
  }, []);

  return (
    <>
      {children}
      <CacheDebugPanel />
    </>
  );
}
