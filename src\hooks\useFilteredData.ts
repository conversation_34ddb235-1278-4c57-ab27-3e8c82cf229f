import { useMemo, useCallback } from 'react';
import { HittingData, VelocityRange } from '../components/dashboard/Hitting/HittingMetricsDashboard';

export const useFilteredData = (
  data: HittingData[],
  exitRanges: VelocityRange[],
  pitchRanges: VelocityRange[]
): HittingData[] => {
  const memoizedFilter = useCallback(
    (hit: HittingData) => {
      if (exitRanges.length === 0 && pitchRanges.length === 0) return true;
      const matchesExit = exitRanges.length === 0 || exitRanges.some(
        range => hit.ExitSpeed >= range.min && hit.ExitSpeed <= range.max
      );
      const matchesPitch = pitchRanges.length === 0 || pitchRanges.some(
        range => hit.RelSpeed >= range.min && hit.RelSpeed <= range.max
      );
      return matchesExit && matchesPitch;
    },
    [exitRanges, pitchRanges]
  );

  return useMemo(
    () => data.filter(memoizedFilter),
    [data, memoizedFilter]
  );
};