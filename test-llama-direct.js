// Direct test of LLaMA server
const testLLaMADirect = async () => {
  try {
    console.log('Testing direct LLaMA server connection...');
    
    const response = await fetch('http://localhost:5001/completion', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        prompt: 'Hello, how are you?',
        max_tokens: 50,
        temperature: 0.7
      }),
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result = await response.json();
    console.log('✅ LLaMA server is working!');
    console.log('Response:', result);
    
  } catch (error) {
    console.error('❌ LLaMA server test failed:', error.message);
  }
};

testLLaMADirect();
