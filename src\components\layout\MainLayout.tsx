"use client";

import React from 'react';
import Sidebar from './Sidebar';
import ChatWidget from '@/components/ai/ChatWidget';

interface MainLayoutProps {
  children: React.ReactNode;
}

export default function MainLayout({ children }: MainLayoutProps) {
  return (
    <div className="flex h-screen bg-gray-50">
      {/* Sidebar */}
      <Sidebar />
      
      {/* Main content */}
      <div className="flex-1 flex flex-col overflow-hidden">
        {/* Main content area */}
        <main className="flex-1 overflow-auto">
          {children}
        </main>
      </div>
      
      {/* AI Chat Widget - positioned globally */}
      <ChatWidget />
    </div>
  );
}
