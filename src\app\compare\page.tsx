"use client";

import React, { useState, useEffect } from 'react';
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, RadarChart, PolarGrid, PolarAngleAxis, PolarRadiusAxis, Radar } from 'recharts';
import { Users, Plus, X } from 'lucide-react';
import MainLayout from '@/components/layout/MainLayout';
import { getStorageData, STORAGE_KEYS } from '@/lib/storage';
import { HittingData } from '@/components/dashboard/Hitting/HittingMetricsDashboard';
import { PitchData } from '@/components/dashboard/PitchingMetricsDashboard';

interface PlayerComparison {
  name: string;
  type: 'hitter' | 'pitcher';
  stats: {
    exitVelocity?: number;
    launchAngle?: number;
    qualityOfContact?: number;
    velocity?: number;
    arsenalScore?: number;
    overallScore: number;
  };
}

export default function PlayerComparison() {
  const [selectedPlayers, setSelectedPlayers] = useState<PlayerComparison[]>([]);
  const [availablePlayers, setAvailablePlayers] = useState<string[]>([]);
  const [selectedPlayerToAdd, setSelectedPlayerToAdd] = useState<string>('');
  const [comparisonType, setComparisonType] = useState<'hitting' | 'pitching' | 'overall'>('overall');

  useEffect(() => {
    loadAvailablePlayers();
  }, []);

  const loadAvailablePlayers = () => {
    const hittingData: HittingData[] = getStorageData(STORAGE_KEYS.HITTING_DATA, []);
    const pitchingData: PitchData[] = getStorageData(STORAGE_KEYS.PITCHING_DATA, []);
    
    const hitters = new Set(hittingData.map(h => h.Batter));
    const pitchers = new Set(pitchingData.map(p => p.Pitcher));
    const allPlayers = Array.from(new Set([...hitters, ...pitchers])).sort();
    
    setAvailablePlayers(allPlayers);
  };

  const addPlayerToComparison = (playerName: string) => {
    if (selectedPlayers.find(p => p.name === playerName) || selectedPlayers.length >= 4) {
      return;
    }

    const hittingData: HittingData[] = getStorageData(STORAGE_KEYS.HITTING_DATA, []);
    const pitchingData: PitchData[] = getStorageData(STORAGE_KEYS.PITCHING_DATA, []);
    
    const playerHits = hittingData.filter(h => h.Batter === playerName);
    const playerPitches = pitchingData.filter(p => p.Pitcher === playerName);
    
    let playerType: 'hitter' | 'pitcher' = 'hitter';
    let stats: PlayerComparison['stats'] = { overallScore: 50 };
    
    if (playerHits.length > 0) {
      const avgExitVelocity = playerHits.reduce((sum, hit) => sum + hit.ExitSpeed, 0) / playerHits.length;
      const avgLaunchAngle = playerHits.reduce((sum, hit) => sum + hit.Angle, 0) / playerHits.length;
      const qualityOfContact = Math.round((avgExitVelocity / 120) * 100);
      
      stats = {
        exitVelocity: Math.round(avgExitVelocity * 100) / 100,
        launchAngle: Math.round(avgLaunchAngle * 100) / 100,
        qualityOfContact,
        overallScore: Math.round((qualityOfContact + (avgLaunchAngle > 0 ? 80 : 60)) / 2)
      };
      playerType = 'hitter';
    }
    
    if (playerPitches.length > 0) {
      const avgVelocity = playerPitches.reduce((sum, pitch) => sum + pitch.RelSpeed, 0) / playerPitches.length;
      const arsenalScore = Math.round((avgVelocity / 100) * 100);
      
      stats = {
        ...stats,
        velocity: Math.round(avgVelocity * 100) / 100,
        arsenalScore,
        overallScore: Math.round((arsenalScore + (stats.qualityOfContact || 50)) / 2)
      };
      
      if (playerPitches.length > playerHits.length) {
        playerType = 'pitcher';
      }
    }
    
    setSelectedPlayers([...selectedPlayers, {
      name: playerName,
      type: playerType,
      stats
    }]);
    
    setSelectedPlayerToAdd('');
  };

  const removePlayerFromComparison = (playerName: string) => {
    setSelectedPlayers(selectedPlayers.filter(p => p.name !== playerName));
  };

  const getComparisonData = () => {
    if (comparisonType === 'hitting') {
      return selectedPlayers
        .filter(p => p.stats.exitVelocity)
        .map(p => ({
          name: p.name,
          'Exit Velocity': p.stats.exitVelocity || 0,
          'Launch Angle': p.stats.launchAngle || 0,
          'Quality of Contact': p.stats.qualityOfContact || 0
        }));
    } else if (comparisonType === 'pitching') {
      return selectedPlayers
        .filter(p => p.stats.velocity)
        .map(p => ({
          name: p.name,
          'Velocity': p.stats.velocity || 0,
          'Arsenal Score': p.stats.arsenalScore || 0
        }));
    } else {
      return selectedPlayers.map(p => ({
        name: p.name,
        'Overall Score': p.stats.overallScore,
        'Type': p.type
      }));
    }
  };

  const getRadarData = () => {
    return selectedPlayers.map(player => ({
      player: player.name,
      'Exit Velocity': player.stats.exitVelocity ? (player.stats.exitVelocity / 120) * 100 : 0,
      'Launch Angle': player.stats.launchAngle ? Math.max(0, (player.stats.launchAngle + 10) * 5) : 0,
      'Quality of Contact': player.stats.qualityOfContact || 0,
      'Velocity': player.stats.velocity ? (player.stats.velocity / 100) * 100 : 0,
      'Arsenal Score': player.stats.arsenalScore || 0,
      'Overall': player.stats.overallScore
    }));
  };

  return (
    <MainLayout>
      <div className="p-6 space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-white">Player Comparison</h1>
            <p className="text-gray-400 mt-2">Dynamic side-by-side player analysis</p>
          </div>
          <div className="flex items-center space-x-4">
            <Select value={comparisonType} onValueChange={(value: any) => setComparisonType(value)}>
              <SelectTrigger className="w-48 bg-gray-800 border-gray-600 text-white">
                <SelectValue />
              </SelectTrigger>
              <SelectContent className="bg-gray-800 border-gray-600">
                <SelectItem value="overall">Overall Comparison</SelectItem>
                <SelectItem value="hitting">Hitting Metrics</SelectItem>
                <SelectItem value="pitching">Pitching Metrics</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        {/* Player Selection */}
        <Card className="floating-card">
          <CardHeader>
            <CardTitle className="text-white flex items-center space-x-2">
              <Users className="h-5 w-5" />
              <span>Select Players to Compare (Max 4)</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center space-x-4 mb-4">
              <Select value={selectedPlayerToAdd} onValueChange={setSelectedPlayerToAdd}>
                <SelectTrigger className="flex-1 bg-gray-800 border-gray-600 text-white">
                  <SelectValue placeholder="Select a player to add" />
                </SelectTrigger>
                <SelectContent className="bg-gray-800 border-gray-600">
                  {availablePlayers
                    .filter(player => !selectedPlayers.find(p => p.name === player))
                    .map(player => (
                      <SelectItem key={player} value={player}>{player}</SelectItem>
                    ))}
                </SelectContent>
              </Select>
              <Button 
                onClick={() => selectedPlayerToAdd && addPlayerToComparison(selectedPlayerToAdd)}
                disabled={!selectedPlayerToAdd || selectedPlayers.length >= 4}
                className="btn-primary"
              >
                <Plus className="h-4 w-4 mr-2" />
                Add Player
              </Button>
            </div>
            
            <div className="flex flex-wrap gap-2">
              {selectedPlayers.map(player => (
                <div key={player.name} className="flex items-center space-x-2 bg-gray-800 rounded-lg px-3 py-2">
                  <span className="text-white">{player.name}</span>
                  <Badge variant={player.type === 'hitter' ? 'default' : 'secondary'}>
                    {player.type}
                  </Badge>
                  <Button
                    size="sm"
                    variant="ghost"
                    onClick={() => removePlayerFromComparison(player.name)}
                    className="h-6 w-6 p-0 text-gray-400 hover:text-white"
                  >
                    <X className="h-3 w-3" />
                  </Button>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {selectedPlayers.length >= 2 && (
          <>
            {/* Comparison Chart */}
            <Card className="floating-card">
              <CardHeader>
                <CardTitle className="text-white">Performance Comparison</CardTitle>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={400}>
                  <BarChart data={getComparisonData()}>
                    <CartesianGrid strokeDasharray="3 3" stroke="rgba(255,255,255,0.1)" />
                    <XAxis dataKey="name" stroke="#ffffff" />
                    <YAxis stroke="#ffffff" />
                    <Tooltip 
                      contentStyle={{ 
                        backgroundColor: '#1a1a2e', 
                        border: '1px solid #e55a2b',
                        borderRadius: '8px',
                        color: '#ffffff'
                      }} 
                    />
                    {comparisonType === 'hitting' && (
                      <>
                        <Bar dataKey="Exit Velocity" fill="#e55a2b" />
                        <Bar dataKey="Launch Angle" fill="#ff7849" />
                        <Bar dataKey="Quality of Contact" fill="#ffa366" />
                      </>
                    )}
                    {comparisonType === 'pitching' && (
                      <>
                        <Bar dataKey="Velocity" fill="#e55a2b" />
                        <Bar dataKey="Arsenal Score" fill="#ff7849" />
                      </>
                    )}
                    {comparisonType === 'overall' && (
                      <Bar dataKey="Overall Score" fill="#e55a2b" />
                    )}
                  </BarChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>

            {/* Radar Chart */}
            <Card className="floating-card">
              <CardHeader>
                <CardTitle className="text-white">Multi-Dimensional Analysis</CardTitle>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={400}>
                  <RadarChart data={getRadarData()}>
                    <PolarGrid stroke="rgba(255,255,255,0.1)" />
                    <PolarAngleAxis dataKey="player" tick={{ fill: '#ffffff', fontSize: 12 }} />
                    <PolarRadiusAxis 
                      angle={90} 
                      domain={[0, 100]} 
                      tick={{ fill: '#ffffff', fontSize: 10 }}
                    />
                    {selectedPlayers.map((player, index) => (
                      <Radar
                        key={player.name}
                        name={player.name}
                        dataKey={player.name}
                        stroke={`hsl(${(index * 60) % 360}, 70%, 50%)`}
                        fill={`hsl(${(index * 60) % 360}, 70%, 50%)`}
                        fillOpacity={0.1}
                        strokeWidth={2}
                      />
                    ))}
                  </RadarChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>

            {/* Detailed Stats Table */}
            <Card className="floating-card">
              <CardHeader>
                <CardTitle className="text-white">Detailed Statistics</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="overflow-x-auto">
                  <table className="w-full text-white">
                    <thead>
                      <tr className="border-b border-gray-600">
                        <th className="text-left py-2">Player</th>
                        <th className="text-left py-2">Type</th>
                        <th className="text-left py-2">Overall Score</th>
                        {comparisonType !== 'pitching' && (
                          <>
                            <th className="text-left py-2">Exit Velocity</th>
                            <th className="text-left py-2">Launch Angle</th>
                            <th className="text-left py-2">Quality of Contact</th>
                          </>
                        )}
                        {comparisonType !== 'hitting' && (
                          <>
                            <th className="text-left py-2">Velocity</th>
                            <th className="text-left py-2">Arsenal Score</th>
                          </>
                        )}
                      </tr>
                    </thead>
                    <tbody>
                      {selectedPlayers.map(player => (
                        <tr key={player.name} className="border-b border-gray-700">
                          <td className="py-2 font-medium">{player.name}</td>
                          <td className="py-2">
                            <Badge variant={player.type === 'hitter' ? 'default' : 'secondary'}>
                              {player.type}
                            </Badge>
                          </td>
                          <td className="py-2">{player.stats.overallScore}</td>
                          {comparisonType !== 'pitching' && (
                            <>
                              <td className="py-2">{player.stats.exitVelocity || '-'}</td>
                              <td className="py-2">{player.stats.launchAngle || '-'}</td>
                              <td className="py-2">{player.stats.qualityOfContact || '-'}</td>
                            </>
                          )}
                          {comparisonType !== 'hitting' && (
                            <>
                              <td className="py-2">{player.stats.velocity || '-'}</td>
                              <td className="py-2">{player.stats.arsenalScore || '-'}</td>
                            </>
                          )}
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </CardContent>
            </Card>
          </>
        )}

        {selectedPlayers.length < 2 && (
          <Card className="floating-card">
            <CardContent className="p-12 text-center">
              <Users className="h-16 w-16 text-gray-400 mx-auto mb-4" />
              <h3 className="text-xl font-semibold text-white mb-2">Select Players to Compare</h3>
              <p className="text-gray-400">Add at least 2 players to start comparing their performance metrics.</p>
            </CardContent>
          </Card>
        )}
      </div>
    </MainLayout>
  );
}
