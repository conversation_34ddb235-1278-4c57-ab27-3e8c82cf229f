import React from 'react';
import { ChevronLeft, ChevronRight } from 'lucide-react';
import { Button } from '@/components/ui/button';

interface EventCarouselProps {
  events: Array<{
    id: string;
    date: string;
    type: string;
    location?: string;
    score?: number;
  }>;
  onEventClick: (eventId: string) => void;
}

const EventCarousel: React.FC<EventCarouselProps> = ({ events, onEventClick }) => {
  const scrollRef = React.useRef<HTMLDivElement>(null);

  const scroll = (direction: 'left' | 'right') => {
    if (scrollRef.current) {
      const scrollAmount = 200;
      scrollRef.current.scrollBy({
        left: direction === 'left' ? -scrollAmount : scrollAmount,
        behavior: 'smooth'
      });
    }
  };

  return (
    <div className="relative">
      <div className="absolute left-0 top-1/2 -translate-y-1/2 z-10">
        <Button
          variant="outline"
          size="icon"
          onClick={() => scroll('left')}
          className="rounded-full bg-white/80 shadow-md"
        >
          <ChevronLeft className="h-4 w-4" />
        </Button>
      </div>

      <div className="absolute right-0 top-1/2 -translate-y-1/2 z-10">
        <Button
          variant="outline"
          size="icon"
          onClick={() => scroll('right')}
          className="rounded-full bg-white/80 shadow-md"
        >
          <ChevronRight className="h-4 w-4" />
        </Button>
      </div>

      <div 
        ref={scrollRef}
        className="flex overflow-x-auto hide-scrollbar gap-4 px-12 py-4 scroll-smooth snap-x"
      >
        {events.map((event) => (
          <div
            key={event.id}
            className="flex-none w-[200px] bg-white rounded-lg shadow-md p-4 cursor-pointer hover:shadow-lg transition-shadow snap-start"
            onClick={() => onEventClick(event.id)}
          >
            <div className="text-sm font-medium text-gray-500">{event.type}</div>
            <div className="mt-1 font-semibold">{new Date(event.date).toLocaleDateString()}</div>
            {event.location && (
              <div className="mt-1 text-sm text-gray-600">{event.location}</div>
            )}
            {event.score !== undefined && (
              <div className="mt-2 text-lg font-bold text-blue-600">{event.score}</div>
            )}
          </div>
        ))}
      </div>

      <style jsx>{`
        .hide-scrollbar::-webkit-scrollbar {
          display: none;
        }
        .hide-scrollbar {
          -ms-overflow-style: none;
          scrollbar-width: none;
        }
      `}</style>
    </div>
  );
};

export default EventCarousel;