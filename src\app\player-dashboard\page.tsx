"use client";

import React, { useState, useEffect } from 'react';
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { User, TrendingUp, Target, Award, Calendar, BarChart3 } from 'lucide-react';
import MainLayout from '@/components/layout/MainLayout';
import { getStorageData, STORAGE_KEYS } from '@/lib/storage';
import { HittingData } from '@/components/dashboard/Hitting/HittingMetricsDashboard';
import { PitchData } from '@/components/dashboard/PitchingMetricsDashboard';
import SlideOverPanel from '@/components/ui/slide-over-panel';

interface PlayerProfile {
  name: string;
  position?: string;
  team?: string;
  level: 'Professional' | 'College' | 'High School';
  baselineScore: number;
  stats: {
    hitting?: {
      avgExitVelocity: number;
      avgLaunchAngle: number;
      qualityOfContactScore: number;
      totalHits: number;
    };
    pitching?: {
      avgVelocity: number;
      arsenalScore: number;
      totalPitches: number;
    };
  };
  milestones: Array<{
    date: string;
    title: string;
    description: string;
    type: 'improvement' | 'achievement' | 'goal';
  }>;
}

export default function PlayerDashboard() {
  const [selectedPlayer, setSelectedPlayer] = useState<string>('');
  const [playerProfile, setPlayerProfile] = useState<PlayerProfile | null>(null);
  const [availablePlayers, setAvailablePlayers] = useState<string[]>([]);
  const [isJourneyOpen, setIsJourneyOpen] = useState(false);
  const [isBenchmarkOpen, setIsBenchmarkOpen] = useState(false);
  const [selectedLevel, setSelectedLevel] = useState<'Professional' | 'College' | 'High School'>('Professional');

  useEffect(() => {
    // Load available players from localStorage
    const hittingData: HittingData[] = getStorageData(STORAGE_KEYS.HITTING_DATA, []);
    const pitchingData: PitchData[] = getStorageData(STORAGE_KEYS.PITCHING_DATA, []);
    
    const hitters = new Set(hittingData.map(h => h.Batter));
    const pitchers = new Set(pitchingData.map(p => p.Pitcher));
    const allPlayers = Array.from(new Set([...hitters, ...pitchers])).sort();
    
    setAvailablePlayers(allPlayers);
    
    if (allPlayers.length > 0 && !selectedPlayer) {
      setSelectedPlayer(allPlayers[0]);
    }
  }, []);

  useEffect(() => {
    if (selectedPlayer) {
      loadPlayerProfile(selectedPlayer);
    }
  }, [selectedPlayer, selectedLevel]);

  const loadPlayerProfile = (playerName: string) => {
    const hittingData: HittingData[] = getStorageData(STORAGE_KEYS.HITTING_DATA, []);
    const pitchingData: PitchData[] = getStorageData(STORAGE_KEYS.PITCHING_DATA, []);
    
    const playerHits = hittingData.filter(h => h.Batter === playerName);
    const playerPitches = pitchingData.filter(p => p.Pitcher === playerName);
    
    // Calculate stats
    const stats: PlayerProfile['stats'] = {};
    
    if (playerHits.length > 0) {
      const avgExitVelocity = playerHits.reduce((sum, hit) => sum + hit.ExitSpeed, 0) / playerHits.length;
      const avgLaunchAngle = playerHits.reduce((sum, hit) => sum + hit.Angle, 0) / playerHits.length;
      
      stats.hitting = {
        avgExitVelocity: Math.round(avgExitVelocity * 100) / 100,
        avgLaunchAngle: Math.round(avgLaunchAngle * 100) / 100,
        qualityOfContactScore: Math.round((avgExitVelocity / 120) * 100), // Simplified calculation
        totalHits: playerHits.length
      };
    }
    
    if (playerPitches.length > 0) {
      const avgVelocity = playerPitches.reduce((sum, pitch) => sum + pitch.RelSpeed, 0) / playerPitches.length;
      
      stats.pitching = {
        avgVelocity: Math.round(avgVelocity * 100) / 100,
        arsenalScore: Math.round((avgVelocity / 100) * 100), // Simplified calculation
        totalPitches: playerPitches.length
      };
    }
    
    // Calculate baseline score (simplified)
    let baselineScore = 50;
    if (stats.hitting) {
      baselineScore += (stats.hitting.qualityOfContactScore - 50) * 0.5;
    }
    if (stats.pitching) {
      baselineScore += (stats.pitching.arsenalScore - 50) * 0.5;
    }
    
    // Sample milestones
    const milestones = [
      {
        date: '2024-01-15',
        title: 'Exit Velocity Milestone',
        description: 'Achieved 100+ mph exit velocity consistently',
        type: 'achievement' as const
      },
      {
        date: '2024-02-01',
        title: 'Launch Angle Improvement',
        description: 'Improved launch angle consistency by 15%',
        type: 'improvement' as const
      },
      {
        date: '2024-03-01',
        title: 'Season Goal',
        description: 'Target: Maintain 95+ mph average exit velocity',
        type: 'goal' as const
      }
    ];
    
    setPlayerProfile({
      name: playerName,
      level: selectedLevel,
      baselineScore: Math.max(0, Math.min(100, Math.round(baselineScore))),
      stats,
      milestones
    });
  };

  const getBenchmarkData = () => {
    if (!playerProfile) return [];
    
    const benchmarks = {
      Professional: { hitting: 95, pitching: 95 },
      College: { hitting: 85, pitching: 85 },
      'High School': { hitting: 75, pitching: 75 }
    };
    
    return [
      {
        metric: 'Hitting Performance',
        player: playerProfile.stats.hitting?.qualityOfContactScore || 0,
        team: benchmarks[selectedLevel].hitting - 5,
        league: benchmarks[selectedLevel].hitting,
        personal: Math.max(playerProfile.stats.hitting?.qualityOfContactScore || 0, benchmarks[selectedLevel].hitting - 10)
      },
      {
        metric: 'Pitching Performance',
        player: playerProfile.stats.pitching?.arsenalScore || 0,
        team: benchmarks[selectedLevel].pitching - 5,
        league: benchmarks[selectedLevel].pitching,
        personal: Math.max(playerProfile.stats.pitching?.arsenalScore || 0, benchmarks[selectedLevel].pitching - 10)
      }
    ];
  };

  return (
    <MainLayout>
      <div className="p-6 space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-white">Player Dashboard</h1>
            <p className="text-gray-400 mt-2">Detailed individual analytics and insights</p>
          </div>
          <div className="flex items-center space-x-4">
            <Select value={selectedLevel} onValueChange={(value: any) => setSelectedLevel(value)}>
              <SelectTrigger className="w-48 bg-gray-800 border-gray-600 text-white">
                <SelectValue />
              </SelectTrigger>
              <SelectContent className="bg-gray-800 border-gray-600">
                <SelectItem value="Professional">Professional</SelectItem>
                <SelectItem value="College">College</SelectItem>
                <SelectItem value="High School">High School</SelectItem>
              </SelectContent>
            </Select>
            <Select value={selectedPlayer} onValueChange={setSelectedPlayer}>
              <SelectTrigger className="w-64 bg-gray-800 border-gray-600 text-white">
                <SelectValue placeholder="Select a player" />
              </SelectTrigger>
              <SelectContent className="bg-gray-800 border-gray-600">
                {availablePlayers.map(player => (
                  <SelectItem key={player} value={player}>{player}</SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </div>

        {playerProfile && (
          <>
            {/* Player Profile Card */}
            <Card className="floating-card">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-4">
                    <div className="w-16 h-16 bg-orange-500 rounded-full flex items-center justify-center">
                      <User className="h-8 w-8 text-white" />
                    </div>
                    <div>
                      <h2 className="text-2xl font-bold text-white">{playerProfile.name}</h2>
                      <div className="flex items-center space-x-2 mt-1">
                        <Badge variant="secondary">{playerProfile.level}</Badge>
                        {playerProfile.position && <Badge variant="outline">{playerProfile.position}</Badge>}
                        {playerProfile.team && <Badge variant="outline">{playerProfile.team}</Badge>}
                      </div>
                    </div>
                  </div>
                  <div className="text-center">
                    <div className="text-4xl font-bold text-orange-500">{playerProfile.baselineScore}</div>
                    <div className="text-sm text-gray-400">Baseline Score</div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Action Buttons */}
            <div className="flex space-x-4">
              <Button 
                onClick={() => setIsJourneyOpen(true)}
                className="btn-primary flex items-center space-x-2"
              >
                <Calendar className="h-4 w-4" />
                <span>Player Journey Timeline</span>
              </Button>
              <Button 
                onClick={() => setIsBenchmarkOpen(true)}
                variant="outline"
                className="border-orange-500 text-orange-500 hover:bg-orange-500 hover:text-white flex items-center space-x-2"
              >
                <BarChart3 className="h-4 w-4" />
                <span>Benchmark Stacking</span>
              </Button>
            </div>

            {/* Stats Grid */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {playerProfile.stats.hitting && (
                <>
                  <Card className="floating-card">
                    <CardContent className="p-6">
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="text-sm font-medium text-gray-400">Exit Velocity</p>
                          <p className="text-2xl font-bold text-white">{playerProfile.stats.hitting.avgExitVelocity} mph</p>
                        </div>
                        <Target className="h-6 w-6 text-orange-500" />
                      </div>
                    </CardContent>
                  </Card>
                  <Card className="floating-card">
                    <CardContent className="p-6">
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="text-sm font-medium text-gray-400">Launch Angle</p>
                          <p className="text-2xl font-bold text-white">{playerProfile.stats.hitting.avgLaunchAngle}°</p>
                        </div>
                        <TrendingUp className="h-6 w-6 text-orange-500" />
                      </div>
                    </CardContent>
                  </Card>
                </>
              )}
              
              {playerProfile.stats.pitching && (
                <>
                  <Card className="floating-card">
                    <CardContent className="p-6">
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="text-sm font-medium text-gray-400">Avg Velocity</p>
                          <p className="text-2xl font-bold text-white">{playerProfile.stats.pitching.avgVelocity} mph</p>
                        </div>
                        <Target className="h-6 w-6 text-orange-500" />
                      </div>
                    </CardContent>
                  </Card>
                  <Card className="floating-card">
                    <CardContent className="p-6">
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="text-sm font-medium text-gray-400">Arsenal Score</p>
                          <p className="text-2xl font-bold text-white">{playerProfile.stats.pitching.arsenalScore}</p>
                        </div>
                        <Award className="h-6 w-6 text-orange-500" />
                      </div>
                    </CardContent>
                  </Card>
                </>
              )}
            </div>
          </>
        )}

        {/* Player Journey Timeline Slide-over */}
        <SlideOverPanel
          isOpen={isJourneyOpen}
          onClose={() => setIsJourneyOpen(false)}
          title="Player Journey Timeline"
        >
          <div className="p-6 space-y-6">
            {playerProfile?.milestones.map((milestone, index) => (
              <div key={index} className="flex items-start space-x-4">
                <div className={`w-3 h-3 rounded-full mt-2 ${
                  milestone.type === 'achievement' ? 'bg-green-500' :
                  milestone.type === 'improvement' ? 'bg-blue-500' : 'bg-orange-500'
                }`} />
                <div className="flex-1">
                  <div className="flex items-center justify-between">
                    <h3 className="font-semibold text-gray-900">{milestone.title}</h3>
                    <span className="text-sm text-gray-500">{milestone.date}</span>
                  </div>
                  <p className="text-gray-600 mt-1">{milestone.description}</p>
                  <Badge 
                    variant={milestone.type === 'achievement' ? 'default' : 'secondary'}
                    className="mt-2"
                  >
                    {milestone.type}
                  </Badge>
                </div>
              </div>
            ))}
          </div>
        </SlideOverPanel>

        {/* Benchmark Stacking Panel */}
        <SlideOverPanel
          isOpen={isBenchmarkOpen}
          onClose={() => setIsBenchmarkOpen(false)}
          title="Benchmark Stacking Analysis"
        >
          <div className="p-6 space-y-6">
            <p className="text-gray-600">
              Compare {playerProfile?.name}'s performance against team, league, and personal benchmarks.
            </p>
            {getBenchmarkData().map((benchmark, index) => (
              <div key={index} className="space-y-2">
                <h3 className="font-semibold text-gray-900">{benchmark.metric}</h3>
                <div className="space-y-2">
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-600">Player</span>
                    <span className="font-medium">{benchmark.player}</span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div 
                      className="bg-orange-500 h-2 rounded-full" 
                      style={{ width: `${benchmark.player}%` }}
                    />
                  </div>
                  <div className="flex justify-between text-xs text-gray-500">
                    <span>Team: {benchmark.team}</span>
                    <span>League: {benchmark.league}</span>
                    <span>Personal Best: {benchmark.personal}</span>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </SlideOverPanel>
      </div>
    </MainLayout>
  );
}
