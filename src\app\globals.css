@import url('https://fonts.googleapis.com/css2?family=Inter:wght@100;200;300;400;500;600;700;800;900&family=Space+Grotesk:wght@300;400;500;600;700&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  /* Professional Dark Theme Colors */
  --background: #0f0f23;
  --foreground: #ffffff;
  --card: #1a1a2e;
  --card-foreground: #ffffff;
  --popover: #1a1a2e;
  --popover-foreground: #ffffff;
  --primary: #e55a2b;
  --primary-foreground: #ffffff;
  --secondary: #16213e;
  --secondary-foreground: #ffffff;
  --muted: #2a2a3e;
  --muted-foreground: #a1a1aa;
  --accent: #e55a2b;
  --accent-foreground: #ffffff;
  --destructive: #ef4444;
  --destructive-foreground: #ffffff;
  --border: #3a3a4e;
  --input: #2a2a3e;
  --ring: #e55a2b;
  --radius: 0.5rem;

  /* Custom brand colors */
  --orange-primary: #e55a2b;
  --orange-secondary: #ff7849;
  --orange-muted: rgba(229, 90, 43, 0.1);
  --orange-shadow: rgba(229, 90, 43, 0.2);

  /* Gradient backgrounds */
  --gradient-primary: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 50%, #16213e 100%);
  --gradient-card: linear-gradient(145deg, #1a1a2e 0%, #16213e 100%);
}

* {
  border-color: hsl(var(--border));
}

body {
  background: var(--gradient-primary);
  color: hsl(var(--foreground));
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  min-height: 100vh;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
  font-family: 'Space Grotesk', 'Inter', sans-serif;
  font-weight: 600;
  color: #ffffff;
}

/* Floating card design with softer orange styling */
.floating-card {
  background: var(--gradient-card);
  border: 1px solid var(--orange-primary);
  border-radius: var(--radius);
  box-shadow: 0 8px 32px var(--orange-shadow);
  backdrop-filter: blur(8px);
  transition: all 0.3s ease;
}

.floating-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 12px 48px var(--orange-shadow);
}

/* Score gauges with white text */
.score-gauge {
  color: #ffffff !important;
}

.score-gauge .gauge-text {
  fill: #ffffff !important;
  color: #ffffff !important;
}

/* Chart styling for white text and grid lines */
.recharts-text {
  fill: #ffffff !important;
}

.recharts-cartesian-axis-line,
.recharts-cartesian-grid-horizontal line,
.recharts-cartesian-grid-vertical line {
  stroke: rgba(255, 255, 255, 0.2) !important;
}

.recharts-legend-item-text {
  color: #ffffff !important;
}

/* Contact Heatmap Tooltip Animation */
@keyframes fadeInScale {
  0% {
    opacity: 0;
    transform: translate(-50%, -100%) scale(0.8);
  }
  100% {
    opacity: 1;
    transform: translate(-50%, -100%) scale(1.05);
  }
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--secondary);
}

::-webkit-scrollbar-thumb {
  background: var(--orange-primary);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--orange-secondary);
}

/* Button enhancements */
.btn-primary {
  background: var(--orange-primary);
  border-color: var(--orange-primary);
  color: #ffffff;
  box-shadow: 0 4px 16px var(--orange-shadow);
}

.btn-primary:hover {
  background: var(--orange-secondary);
  box-shadow: 0 6px 24px var(--orange-shadow);
}

/* Card enhancements */
.card {
  background: var(--gradient-card);
  border: 1px solid var(--border);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

/* Input styling */
.input {
  background: var(--input);
  border: 1px solid var(--border);
  color: var(--foreground);
}

.input:focus {
  border-color: var(--orange-primary);
  box-shadow: 0 0 0 2px var(--orange-muted);
}