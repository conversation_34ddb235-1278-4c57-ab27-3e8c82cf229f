"use client"

import React, { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/card';
import {
  clearAllStorageData,
  getAllStorageData,
  initializeStorage,
  forceClearAllCaches,
  STORAGE_KEYS
} from '@/lib/storage';
import { Trash2, RefreshCw, Eye, EyeOff } from 'lucide-react';

const CacheDebugPanel: React.FC = () => {
  const [isVisible, setIsVisible] = useState(false);
  const [storageData, setStorageData] = useState<Record<string, any>>({});
  const [showData, setShowData] = useState(false);

  const refreshStorageData = () => {
    const data = getAllStorageData();
    setStorageData(data);
  };

  const handleClearAll = () => {
    if (window.confirm('Are you sure you want to clear all cached data? This will reset the application state.')) {
      clearAllStorageData();
      refreshStorageData();
      // Reload the page to reset the application state
      window.location.reload();
    }
  };

  const handleReinitialize = () => {
    initializeStorage();
    refreshStorageData();
  };

  const handleClearSpecific = (key: string) => {
    if (window.confirm(`Are you sure you want to clear "${key}"?`)) {
      localStorage.removeItem(key);
      refreshStorageData();
    }
  };

  // Only show in development mode
  if (process.env.NODE_ENV !== 'development') {
    return null;
  }

  return (
    <div className="fixed bottom-4 right-4 z-50">
      {!isVisible ? (
        <Button
          onClick={() => {
            setIsVisible(true);
            refreshStorageData();
          }}
          variant="outline"
          size="sm"
          className="bg-red-500 text-white border-red-600 hover:bg-red-600"
        >
          🐛 Debug Cache
        </Button>
      ) : (
        <Card className="w-96 max-h-96 overflow-auto bg-white shadow-lg border-2 border-red-500">
          <CardHeader className="pb-2">
            <div className="flex items-center justify-between">
              <CardTitle className="text-sm text-red-600">Cache Debug Panel</CardTitle>
              <Button
                onClick={() => setIsVisible(false)}
                variant="ghost"
                size="sm"
                className="h-6 w-6 p-0"
              >
                ×
              </Button>
            </div>
          </CardHeader>
          <CardContent className="space-y-2">
            <div className="flex flex-wrap gap-1">
              <Button
                onClick={() => {
                  if (window.confirm('FORCE CLEAR: This will clear ALL caches and reload the page. Continue?')) {
                    forceClearAllCaches();
                  }
                }}
                variant="destructive"
                size="sm"
                className="text-xs bg-red-600 hover:bg-red-700"
              >
                🔥 Force Clear
              </Button>
              <Button
                onClick={handleClearAll}
                variant="destructive"
                size="sm"
                className="text-xs"
              >
                <Trash2 className="w-3 h-3 mr-1" />
                Clear All
              </Button>
              <Button
                onClick={handleReinitialize}
                variant="outline"
                size="sm"
                className="text-xs"
              >
                <RefreshCw className="w-3 h-3 mr-1" />
                Reinitialize
              </Button>
              <Button
                onClick={() => {
                  setShowData(!showData);
                  if (!showData) refreshStorageData();
                }}
                variant="outline"
                size="sm"
                className="text-xs"
              >
                {showData ? <EyeOff className="w-3 h-3 mr-1" /> : <Eye className="w-3 h-3 mr-1" />}
                {showData ? 'Hide' : 'Show'} Data
              </Button>
            </div>

            {showData && (
              <div className="space-y-2 max-h-48 overflow-auto">
                <div className="text-xs font-semibold text-gray-600">Stored Data:</div>
                {Object.keys(STORAGE_KEYS).length === 0 ? (
                  <div className="text-xs text-gray-500">No data stored</div>
                ) : (
                  Object.entries(STORAGE_KEYS).map(([keyName, keyValue]) => {
                    const data = storageData[keyValue];
                    return (
                      <div key={keyName} className="border rounded p-2 bg-gray-50">
                        <div className="flex items-center justify-between">
                          <span className="text-xs font-medium">{keyName}</span>
                          <Button
                            onClick={() => handleClearSpecific(keyValue)}
                            variant="ghost"
                            size="sm"
                            className="h-4 w-4 p-0 text-red-500 hover:text-red-700"
                          >
                            <Trash2 className="w-3 h-3" />
                          </Button>
                        </div>
                        <div className="text-xs text-gray-600 mt-1">
                          {data ? (
                            Array.isArray(data) ? (
                              `Array with ${data.length} items`
                            ) : typeof data === 'object' ? (
                              'Object'
                            ) : (
                              String(data).substring(0, 50) + (String(data).length > 50 ? '...' : '')
                            )
                          ) : (
                            'No data'
                          )}
                        </div>
                      </div>
                    );
                  })
                )}
              </div>
            )}

            <div className="text-xs text-gray-500 mt-2">
              💡 Tip: Use "Clear All" if the app behaves unexpectedly
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default CacheDebugPanel;
