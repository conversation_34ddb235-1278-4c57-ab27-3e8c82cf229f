This is a [Next.js](https://nextjs.org) project bootstrapped with [`create-next-app`](https://nextjs.org/docs/app/api-reference/cli/create-next-app).

## Getting Started

First, run the development server:

```bash
npm run dev
# or
yarn dev
# or
pnpm dev
# or
bun dev
```

Open [http://localhost:3000](http://localhost:3000) with your browser to see the result.

You can start editing the page by modifying `app/page.tsx`. The page auto-updates as you edit the file.

This project uses [`next/font`](https://nextjs.org/docs/app/building-your-application/optimizing/fonts) to automatically optimize and load [Geist](https://vercel.com/font), a new font family for Vercel.

## Cache Management

This application includes comprehensive cache management to prevent issues with stale data:

### 🚨 If the app only works after deleting .next folder:

**SOLUTION - Use these commands instead of manually deleting:**

```bash
# RECOMMENDED: Use the nuclear option first
npm run dev:nuclear

# If that doesn't work, try these in order:
npm run dev:force
npm run dev:clean
npm run clear-cache && npm run dev

# Windows users: Double-click start-fresh.bat
```

### Available Commands (in order of aggressiveness):

```bash
npm run dev              # Normal development
npm run dev:clean        # Clear cache + start dev server
npm run dev:fresh        # Clear cache + disable caching + start dev
npm run dev:force        # Clear cache + wait longer + start dev
npm run dev:nuclear      # NUCLEAR: Kill processes + aggressive clear + start dev

npm run clear-cache      # Just clear the .next folder
npm run nuclear-clear    # NUCLEAR: Kill all processes + aggressive clear
npm run reset           # Clear everything and show instructions
```

### 🔥 NUCLEAR OPTION (When nothing else works):

```bash
# Step 1: Close your IDE/editor completely
# Step 2: Run the nuclear command
npm run dev:nuclear

# Alternative: Use the batch file
start-fresh.bat
```

### Cache Types Handled:

1. **Next.js Build Cache** (`.next` folder)
   - Automatically cleared by scripts above
   - Uses PowerShell on Windows for better file handling
   - Contains compiled pages, webpack cache, etc.

2. **Browser localStorage Cache**
   - Managed automatically with version checking
   - Aggressive clearing in development mode
   - Use the debug panel for manual control

3. **Webpack Module Cache**
   - Disabled in development mode
   - Prevents module resolution issues

### Debug Panel (Development Mode Only)

In development mode, you'll see a "🐛 Debug Cache" button in the bottom-right corner:

- **🔥 Force Clear**: Nuclear option - clears ALL caches and reloads
- **Clear All**: Clear localStorage data
- **Show Data**: View all stored data
- **Reinitialize**: Reset storage system

### Automatic Cache Management

The app automatically:
- Clears stale cache every 2 minutes in development
- Validates localStorage data structure
- Handles server restarts gracefully
- Provides fallback values for corrupted data
- Detects build changes and clears cache accordingly

### Why This Happens

The caching issue occurs because:
1. Next.js aggressively caches compiled pages and modules
2. localStorage data can become stale between sessions
3. Webpack module resolution gets cached
4. Hot reload can sometimes fail to update properly

### Prevention

- Use `npm run dev:clean` when starting development
- The debug panel will help identify cache issues
- Automatic cache clearing prevents most problems

## Learn More

To learn more about Next.js, take a look at the following resources:

- [Next.js Documentation](https://nextjs.org/docs) - learn about Next.js features and API.
- [Learn Next.js](https://nextjs.org/learn) - an interactive Next.js tutorial.

You can check out [the Next.js GitHub repository](https://github.com/vercel/next.js) - your feedback and contributions are welcome!

## Deploy on Vercel

The easiest way to deploy your Next.js app is to use the [Vercel Platform](https://vercel.com/new?utm_medium=default-template&filter=next.js&utm_source=create-next-app&utm_campaign=create-next-app-readme) from the creators of Next.js.

Check out our [Next.js deployment documentation](https://nextjs.org/docs/app/building-your-application/deploying) for more details.
