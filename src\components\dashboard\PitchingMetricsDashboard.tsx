"use client";

import React, { useState, useMemo, useRef, ChangeEvent } from 'react';
import {
  <PERSON>Chart,
  Line,
  XAxis,
  <PERSON>A<PERSON>s,
  CartesianGrid,
  <PERSON>lt<PERSON>,
  Legend,
  ResponsiveContainer,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  Bar,
  ReferenceLine
} from 'recharts';
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/card';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select';
import { Button } from '@/components/ui/button';
import PdfBuilderModal from './shared/PdfBuilderModal';
import { Upload, FileText } from 'lucide-react';
import CoachDashboard from './CoachDashboard';
import { useRouter } from 'next/navigation';
import ChatWidget from '@/components/ai/ChatWidget';

// -----------------------------
// Type Definitions
// -----------------------------
export interface PitchData {
  Pitcher: string;
  AutoPitchType: string;
  RelSpeed: number;
  SpinRate: number;
  VertBreak: number;
  HorzBreak: number;
  RelSide: number;
  RelHeight: number;
  Extension: number;
  EventID?: string;
  EventDate?: string;
  EventLocation?: string;
  EventType?: string;
  SessionID?: string;
  NextEvent?: string;
}

interface LevelBenchmark {
  velocity: number;
  spinRate: number;
  vertBreak: number;
  horzBreak: number;
}

interface PitchTypeMetrics {
  avgVelocity: number;
  avgSpinRate: number;
  avgVertBreak: number;
  avgHorzBreak: number;
  pitchScore?: PitchScore;
  velocitySeparationScore?: number;
  movementScore?: number;
  releasePointScore?: number;
  singlePitchArsenalScore?: number;
}

interface PitchScore {
  velocityScore: number;
  movementScore: number;
  spinScore: number;
  releaseScore: number;
  totalScore: number;
}

interface PitchScoreParams {
  weight_velocity: number;
  weight_movement: number;
  weight_spin: number;
  weight_release_point: number;
  velocity_bonus_factor: number;
  velocity_penalty_factor: number;
  vertical_break_bonus_factor: number;
  vertical_break_penalty_factor: number;
  horizontal_break_bonus_factor: number;
  horizontal_break_penalty_factor: number;
  spin_rate_bonus_factor: number;
  spin_rate_penalty_factor: number;
  release_penalty_factor: number;
  acceptable_release_side_diff: number;
  acceptable_release_height_diff: number;
}

interface PitchTypeWeights {
  [key: string]: {
    weight_velocity: number;
    weight_movement: number;
    weight_spin: number;
    weight_release_point: number;
  };
}

interface ArsenalScoreParams {
  velocity_separation_minimum: number;
  velocity_separation_penalty: number;
  movement_separation_minimum: number;
  movement_separation_penalty: number;
  release_point_similarity_acceptable: number;
  release_point_similarity_penalty: number;
}

export type PitchTypes =
  | 'FF'
  | 'SL'
  | 'CH'
  | 'CU'
  | 'Sinker'
  | 'Sweeper'
  | 'Splitter'
  | 'Slurve'
  | '2-Seam'
  | '12-6 CB'
  | 'Gyro Slider';
type Level = 'Professional' | 'College' | 'High School';

interface LevelBenchmarks {
  [key: string]: {
    [K in PitchTypes]: LevelBenchmark;
  };
}

// -----------------------------
// Helper Functions
// -----------------------------

// Helper to calculate average of an array of numbers.
const calculateAverage = (data: number[]): number =>
  data.length ? data.reduce((sum, n) => sum + n, 0) / data.length : 0;

// Helper to calculate an "average pitch" from an array of pitches.
export const calculateAveragePitch = (pitches: PitchData[]): PitchData => ({
  AutoPitchType: pitches[0].AutoPitchType,
  Pitcher: pitches[0].Pitcher,
  RelSpeed: calculateAverage(pitches.map(p => p.RelSpeed)),
  SpinRate: calculateAverage(pitches.map(p => p.SpinRate)),
  VertBreak: calculateAverage(pitches.map(p => p.VertBreak)),
  HorzBreak: calculateAverage(pitches.map(p => p.HorzBreak)),
  RelSide: calculateAverage(pitches.map(p => p.RelSide)),
  RelHeight: calculateAverage(pitches.map(p => p.RelHeight)),
  Extension: calculateAverage(pitches.map(p => p.Extension))
});

// -----------------------------
// UI Components
// -----------------------------

export const ScoreGauge: React.FC<{
  score: number;
  size?: 'sm' | 'md' | 'lg';
  label?: string;
}> = ({ score, size = 'md', label }) => {
  const normalizedScore = Math.min(100, Math.max(0, score));
  const radius = size === 'sm' ? 25 : size === 'md' ? 35 : 45;
  const strokeWidth = size === 'sm' ? 4 : size === 'md' ? 6 : 8;
  const circumference = 2 * Math.PI * radius;
  const progress = ((100 - normalizedScore) / 100) * circumference;

  const getColor = () => {
    if (normalizedScore >= 80) return '#e55a2b'; // Softer burnt orange for excellent
    if (normalizedScore >= 60) return '#ff8c5a'; // Lighter orange for good
    return '#ff6b6b'; // Softer red for poor
  };

  const dimensions = {
    sm: 'w-16 h-16',
    md: 'w-24 h-24',
    lg: 'w-32 h-32'
  }[size];

  const textSize = {
    sm: 'text-lg',
    md: 'text-2xl',
    lg: 'text-3xl'
  }[size];

  const labelSize = {
    sm: 'text-xs',
    md: 'text-sm',
    lg: 'text-base'
  }[size];

  return (
    <div className="flex flex-col items-center">
      <div className={`relative ${dimensions} flex items-center justify-center`}>
        <svg className="w-full h-full transform -rotate-90">
          {/* Background circle */}
          <circle
            cx="50%"
            cy="50%"
            r={radius}
            fill="none"
            stroke="#374151"
            strokeWidth={strokeWidth}
            className="transition-all duration-300"
          />
          {/* Progress circle */}
          <circle
            cx="50%"
            cy="50%"
            r={radius}
            fill="none"
            stroke={getColor()}
            strokeWidth={strokeWidth}
            strokeDasharray={circumference}
            strokeDashoffset={progress}
            className="transition-all duration-300"
            strokeLinecap="round"
          />
        </svg>
        <div className="absolute flex flex-col items-center justify-center">
          <span className={`${textSize} font-bold text-white`}>
            {normalizedScore.toFixed(1)}
          </span>
        </div>
      </div>
      {label && (
        <span className={`${labelSize} text-white mt-1`}>{label}</span>
      )}
    </div>
  );
};

const LoadingScreen = () => {
  const animationStyles = `
    .lineAnimation {
      animation: waveUp 1s ease-in-out infinite;
    }
    @keyframes waveUp {
      0%, 100% {
        transform: scaleY(0.2);
      }
      50% {
        transform: scaleY(1);
      }
    }
  `;
  return (
    <>
      <style>{animationStyles}</style>
      <div className="fixed inset-0 bg-white flex items-center justify-center z-50">
        <div className="text-center">
          <div className="w-64 h-64 mx-auto relative">
            <svg viewBox="0 0 100 100" className="w-full h-full">
              <path
                d="M20,20 L80,20 L80,50 L50,70 L20,50 Z"
                fill="none"
                stroke="black"
                strokeWidth="2"
              />
              <line x1="20" y1="20" x2="80" y2="20" stroke="black" strokeWidth="2" />
              <text
                x="50"
                y="20"
                textAnchor="middle"
                fontSize="9"
                fontWeight="bold"
                fill="white"
                strokeWidth="4"
                stroke="white"
              >
                BASELINE
              </text>
              <text x="50" y="23" textAnchor="middle" fontSize="9" fontWeight="bold">
                BASELINE
              </text>
              <circle cx="20" cy="20" r="3" fill="black" />
              <circle cx="80" cy="20" r="3" fill="black" />
              <circle cx="20" cy="50" r="3" fill="black" />
              <circle cx="80" cy="50" r="3" fill="black" />
              {[{ x: 30, y: 51 }, { x: 40, y: 57 }, { x: 50, y: 61 }, { x: 60, y: 57 }, { x: 70, y: 51 }].map(
                (pos, i) => (
                  <g key={i}>
                    <line
                      x1={pos.x}
                      y1={pos.y - 25}
                      x2={pos.x}
                      y2={pos.y}
                      stroke="black"
                      strokeWidth="2"
                      className="lineAnimation"
                      style={{
                        animationDelay: `${i * 0.2}s`,
                        transformOrigin: `${pos.x}px ${pos.y}px`
                      }}
                    />
                    <circle cx={pos.x} cy={pos.y} r="2.5" fill="black" />
                  </g>
                )
              )}
              <circle cx="50" cy="70" r="3" fill="black" />
            </svg>
          </div>
          <p className="mt-4 text-lg font-semibold">Loading...</p>
        </div>
      </div>
    </>
  );
};

// -----------------------------
// Constants and Default Parameters
// -----------------------------
export const LEVEL_BENCHMARKS: LevelBenchmarks = {
  Professional: {
    FF: { velocity: 86, spinRate: 2250, vertBreak: 15, horzBreak: 7 },
    SL: { velocity: 86, spinRate: 2500, vertBreak: 2, horzBreak: -5 },
    CH: { velocity: 85, spinRate: 1900, vertBreak: 2, horzBreak: 13 },
    CU: { velocity: 83, spinRate: 2350, vertBreak: -8, horzBreak: -5 },
    Sinker: { velocity: 89, spinRate: 2350, vertBreak: -8, horzBreak: -5 },
    Sweeper: { velocity: 82, spinRate: 2300, vertBreak: -1, horzBreak: -14 },
    Splitter: { velocity: 84, spinRate: 1500, vertBreak: 3, horzBreak: 10 },
    Slurve: { velocity: 78, spinRate: 2150, vertBreak: -6, horzBreak: -12 },
    '2-Seam': { velocity: 90, spinRate: 2100, vertBreak: 6, horzBreak: -8 },
    '12-6 CB': { velocity: 80, spinRate: 2300, vertBreak: -10, horzBreak: -6 },
    'Gyro Slider': { velocity: 87, spinRate: 2150, vertBreak: 2, horzBreak: -4 }
  },
  College: {
    FF: { velocity: 84, spinRate: 2200, vertBreak: 11, horzBreak: 5 },
    SL: { velocity: 80, spinRate: 2400, vertBreak: 1, horzBreak: -5 },
    CH: { velocity: 80, spinRate: 1700, vertBreak: 3, horzBreak: 9 },
    CU: { velocity: 77, spinRate: 2300, vertBreak: -7, horzBreak: -7 },
    Sinker: { velocity: 84, spinRate: 1900, vertBreak: 3, horzBreak: -4 },
    Sweeper: { velocity: 77, spinRate: 2250, vertBreak: 0, horzBreak: -10 },
    Splitter: { velocity: 78, spinRate: 1400, vertBreak: -2, horzBreak: 5 },
    Slurve: { velocity: 76, spinRate: 2000, vertBreak: -5, horzBreak: -7 },
    '2-Seam': { velocity: 87, spinRate: 2000, vertBreak: 5, horzBreak: 7 },
    '12-6 CB': { velocity: 77, spinRate: 2250, vertBreak: -9, horzBreak: -3 },
    'Gyro Slider': { velocity: 81, spinRate: 2100, vertBreak: 1, horzBreak: -3 }
  },
  "High School": {
    FF: { velocity: 80, spinRate: 2000, vertBreak: 9, horzBreak: 3 },
    SL: { velocity: 73, spinRate: 2150, vertBreak: 0, horzBreak: -4 },
    CH: { velocity: 74, spinRate: 1500, vertBreak: 2, horzBreak: 7 },
    CU: { velocity: 70, spinRate: 2050, vertBreak: -5, horzBreak: -6 },
    Sinker: { velocity: 76, spinRate: 1800, vertBreak: 2, horzBreak: 7 },
    Sweeper: { velocity: 70, spinRate: 2300, vertBreak: -1, horzBreak: -8 },
    Splitter: { velocity: 72, spinRate: 1300, vertBreak: -1, horzBreak: 4 },
    Slurve: { velocity: 69, spinRate: 2200, vertBreak: -4, horzBreak: -6 },
    '2-Seam': { velocity: 78, spinRate: 1650, vertBreak: 4, horzBreak: 6 },
    '12-6 CB': { velocity: 70, spinRate: 2300, vertBreak: -8, horzBreak: -2 },
    'Gyro Slider': { velocity: 75, spinRate: 1750, vertBreak: 0, horzBreak: -2 }
  }
};

const PITCH_TYPE_WEIGHTS: PitchTypeWeights = {
  FF: {
    weight_velocity: 0.7,
    weight_movement: 0.2,
    weight_spin: 0,
    weight_release_point: 0.2
  },
  SL: {
    weight_velocity: 0.8,
    weight_movement: 0.1,
    weight_spin: 0,
    weight_release_point: 0.1
  },
  CH: {
    weight_velocity: 0.4,
    weight_movement: 0.3,
    weight_spin: 0,
    weight_release_point: 0.3
  },
  CU: {
    weight_velocity: 0.3,
    weight_movement: 0.6,
    weight_spin: 0,
    weight_release_point: 0.1
  },
  Sinker: {
    weight_velocity: 0.5,
    weight_movement: 0.4,
    weight_spin: 0,
    weight_release_point: 0.1
  },
  Sweeper: {
    weight_velocity: 0.3,
    weight_movement: 0.6,
    weight_spin: 0,
    weight_release_point: 0.1
  },
  Splitter: {
    weight_velocity: 0.3,
    weight_movement: 0.6,
    weight_spin: 0,
    weight_release_point: 0.1
  }
};

const DEFAULT_PITCH_SCORE_PARAMS: PitchScoreParams = {
  weight_velocity: 0.5,
  weight_movement: 0.4,
  weight_spin: 0,
  weight_release_point: 0.1,
  velocity_bonus_factor: 5.5,
  velocity_penalty_factor: 1.5,
  vertical_break_bonus_factor: 10,
  vertical_break_penalty_factor: 5,
  horizontal_break_bonus_factor: 10,
  horizontal_break_penalty_factor: 5,
  spin_rate_bonus_factor: 0.05,
  spin_rate_penalty_factor: 0.02,
  release_penalty_factor: 1,
  acceptable_release_side_diff: 1,
  acceptable_release_height_diff: 0.5
};

const DEFAULT_ARSENAL_SCORE_PARAMS: ArsenalScoreParams = {
  velocity_separation_minimum: 6,
  velocity_separation_penalty: 1,
  movement_separation_minimum: 15,
  movement_separation_penalty: 1,
  release_point_similarity_acceptable: 0.5,
  release_point_similarity_penalty: 1
};

// -----------------------------
// Score Calculations
// -----------------------------

const calculateVariance = (numbers: number[]): number => {
  const mean = calculateAverage(numbers);
  const squareDiffs = numbers.map(num => Math.pow(num - mean, 2));
  return calculateAverage(squareDiffs);
};

export const calculateSinglePitchScore = (
  pitch: PitchData,
  benchmarks: LevelBenchmark,
  pitcherData: PitchData[],
  params: PitchScoreParams = DEFAULT_PITCH_SCORE_PARAMS
): PitchScore => {
  // 1) Velocity Score
  const velocityDiff = pitch.RelSpeed - benchmarks.velocity;
  let velocityScore = 70;
  if (velocityDiff > 0) {
    velocityScore = 30 + velocityDiff * params.velocity_bonus_factor;
  } else if (velocityDiff < 0) {
    velocityScore = 30 + velocityDiff * params.velocity_penalty_factor;
  } else {
    velocityScore = 30;
  }
  velocityScore = Math.max(0, Math.min(100, velocityScore));

  // 2) Movement Score
  const verticalBreakDiff = pitch.VertBreak - benchmarks.vertBreak;
  const horizontalBreakDiff = pitch.HorzBreak - benchmarks.horzBreak;
  let vbScore = 50;
  if (verticalBreakDiff >= 0) {
    vbScore = Math.min(100, vbScore + verticalBreakDiff * params.vertical_break_bonus_factor);
  } else {
    vbScore = Math.max(0, vbScore + verticalBreakDiff * params.vertical_break_penalty_factor);
  }
  let hbScore = 50;
  if (horizontalBreakDiff >= 0) {
    hbScore = Math.min(100, hbScore + horizontalBreakDiff * params.horizontal_break_bonus_factor);
  } else {
    hbScore = Math.max(0, hbScore + horizontalBreakDiff * params.horizontal_break_penalty_factor);
  }
  const movementScore = (vbScore + hbScore) / 2;

  // 3) Spin Rate Score
  const spinRateDiff = pitch.SpinRate - benchmarks.spinRate;
  let spinScore = 50;
  if (spinRateDiff >= 0) {
    spinScore = Math.min(100, spinScore + spinRateDiff * params.spin_rate_bonus_factor);
  } else {
    spinScore = Math.max(0, spinScore + spinRateDiff * params.spin_rate_penalty_factor);
  }

  // 4) Release Point Score
  const samePitchTypes = pitcherData.filter(p => p.AutoPitchType === pitch.AutoPitchType);
  let releaseScore = 0;
  if (samePitchTypes.length > 0) {
    const meanRelSide = calculateAverage(samePitchTypes.map(p => p.RelSide));
    const meanRelHeight = calculateAverage(samePitchTypes.map(p => p.RelHeight));
    const relSideDeviation = Math.abs(pitch.RelSide - meanRelSide);
    const relHeightDeviation = Math.abs(pitch.RelHeight - meanRelHeight);
    if (
      relSideDeviation <= params.acceptable_release_side_diff &&
      relHeightDeviation <= params.acceptable_release_height_diff
    ) {
      releaseScore = 100;
    } else {
      const penalty = (relSideDeviation + relHeightDeviation) * params.release_penalty_factor;
      releaseScore = Math.max(0, 100 - penalty);
    }
  }

  // 5) Combine Weighted Scores
  const weights = PITCH_TYPE_WEIGHTS[pitch.AutoPitchType] || {
    weight_velocity: 0.5,
    weight_movement: 0.4,
    weight_spin: 0,
    weight_release_point: 0.1
  };

  const totalScore =
    velocityScore * weights.weight_velocity +
    movementScore * weights.weight_movement +
    spinScore * weights.weight_spin +
    releaseScore * weights.weight_release_point;

  return {
    velocityScore,
    movementScore,
    spinScore,
    releaseScore,
    totalScore
  };
};

export const calculateArsenalScore = (
  pitches: PitchData[],
  params: ArsenalScoreParams = DEFAULT_ARSENAL_SCORE_PARAMS
): number => {
  if (pitches.length < 2) return 0;

  let totalScore = 100;
  const pitchTypes = [...new Set(pitches.map(p => p.AutoPitchType))];

  // Calculate average metrics for each pitch type
  const pitchMetrics = pitchTypes.map(type => {
    const typePitches = pitches.filter(p => p.AutoPitchType === type);
    return {
      type,
      avgVelocity: calculateAverage(typePitches.map(p => p.RelSpeed)),
      avgVertBreak: calculateAverage(typePitches.map(p => p.VertBreak)),
      avgHorzBreak: calculateAverage(typePitches.map(p => p.HorzBreak)),
      avgRelSide: calculateAverage(typePitches.map(p => p.RelSide)),
      avgRelHeight: calculateAverage(typePitches.map(p => p.RelHeight))
    };
  });

  // Velocity Separation
  for (let i = 0; i < pitchMetrics.length; i++) {
    for (let j = i + 1; j < pitchMetrics.length; j++) {
      const velDiff = Math.abs(pitchMetrics[i].avgVelocity - pitchMetrics[j].avgVelocity);
      if (velDiff < params.velocity_separation_minimum) {
        totalScore -= (params.velocity_separation_minimum - velDiff) * params.velocity_separation_penalty;
      }
    }
  }

  // Movement Separation
  for (let i = 0; i < pitchMetrics.length; i++) {
    for (let j = i + 1; j < pitchMetrics.length; j++) {
      const movementDiff = Math.sqrt(
        Math.pow(pitchMetrics[i].avgVertBreak - pitchMetrics[j].avgVertBreak, 2) +
          Math.pow(pitchMetrics[i].avgHorzBreak - pitchMetrics[j].avgHorzBreak, 2)
      );
      if (movementDiff < params.movement_separation_minimum) {
        totalScore -= (params.movement_separation_minimum - movementDiff) * params.movement_separation_penalty;
      }
    }
  }

  // Release Point Similarity
  for (let i = 0; i < pitchMetrics.length; i++) {
    for (let j = i + 1; j < pitchMetrics.length; j++) {
      const releaseDiff = Math.sqrt(
        Math.pow(pitchMetrics[i].avgRelSide - pitchMetrics[j].avgRelSide, 2) +
          Math.pow(pitchMetrics[i].avgRelHeight - pitchMetrics[j].avgRelHeight, 2)
      );
      if (releaseDiff > params.release_point_similarity_acceptable) {
        totalScore -= (releaseDiff - params.release_point_similarity_acceptable) * params.release_point_similarity_penalty;
      }
    }
  }

  return Math.max(0, Math.min(100, totalScore));
};

// -----------------------------
// Main Component
// -----------------------------
const commonChartMargin = { top: 20, right: 30, left: 20, bottom: 5 };

const PitchingMetricsDashboard = (): React.ReactElement => {
  const router = useRouter();
  const [data, setData] = useState<PitchData[]>([]);
  const [selectedPitcher, setSelectedPitcher] = useState<string>('');
  const [selectedLevel, setSelectedLevel] = useState<Level>('Professional');
  const [uploadError, setUploadError] = useState<string>('');
  const [currentPitchIndex, setCurrentPitchIndex] = useState(0);
  const [isLoading, setIsLoading] = useState(false);
  const [showAdmin, setShowAdmin] = useState(false);
  const [showArsenalDetails, setShowArsenalDetails] = useState(false);
  const [viewMode, setViewMode] = useState<'player' | 'coach'>('player');
  const [isPdfBuilderOpen, setIsPdfBuilderOpen] = useState(false);
  const [showEventTimeline, setShowEventTimeline] = useState(false);
  const [eventHistory, setEventHistory] = useState<{ [key: string]: any } | null>(null);
  const [selectedEvent, setSelectedEvent] = useState<string | null>(null);
  const [xDomain, setXDomain] = useState([-20, 20]);
  const [yDomain, setYDomain] = useState([-20, 20]);
  const [movementZoom, setMovementZoom] = useState(1);
  const [releaseZoom, setReleaseZoom] = useState(1);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // -----------------------------
  // CSV Header Mapping for Normalization
  // -----------------------------
  const headerMapping: { [key: string]: string } = {
    'horzbreak': 'HorzBreak',
    'eventid': 'EventID',
    'event date': 'EventDate',
    'event location': 'EventLocation',
    'event type': 'EventType',
    'session id': 'SessionID',
    'next event': 'NextEvent'
  };

  // -----------------------------
  // Derived Data using useMemo
  // -----------------------------
  const pitchers = useMemo(() => {
    return [...new Set(data.map(pitch => pitch.Pitcher))];
  }, [data]);

  const handleUploadClick = () => {
    fileInputRef.current?.click();
  };

  const handleFileUpload = (event: ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    setIsLoading(true);
    setUploadError('');
    setData([]);
    setSelectedPitcher('');

    if (!file.name.endsWith('.csv')) {
      setUploadError('Please upload a valid CSV file');
      setIsLoading(false);
      return;
    }

    const reader = new FileReader();
    reader.onload = (e) => {
      setTimeout(() => {
        try {
          const text = e.target?.result as string;
          const rows = text.split('\n');
          const headers = rows[0].split(',');
          const parsedData = rows
            .slice(1)
            .filter(row => row.trim())
            .map(row => {
              const values = row.split(',');
              const obj: Partial<PitchData> = {};
              headers.forEach((header, index) => {
                const normalizedHeader =
                  headerMapping[header.trim().toLowerCase()] || header.trim();
                const key = normalizedHeader as keyof PitchData;
                let value = values[index]?.trim();
                if (
                  key === 'Pitcher' ||
                  key === 'AutoPitchType' ||
                  key === 'EventID' ||
                  key === 'EventDate' ||
                  key === 'EventLocation' ||
                  key === 'EventType' ||
                  key === 'SessionID' ||
                  key === 'NextEvent'
                ) {
                  obj[key] = value || '';
                } else {
                  const num = Number(value);
                  obj[key] = isNaN(num) ? 0 : num;
                }
              });
              return obj as PitchData;
            })
            .filter(row => row.Pitcher && row.Pitcher.trim());

          if (parsedData.length === 0) {
            setUploadError('No valid data found in CSV');
            setIsLoading(false);
            return;
          }

          console.log("Sample row with event data:", parsedData[0]);
          setData(parsedData);
          if (fileInputRef.current) {
            fileInputRef.current.value = '';
          }
          setIsLoading(false);
        } catch (error) {
          console.error('Error parsing CSV:', error);
          setUploadError('Error parsing CSV file');
          setIsLoading(false);
        }
      }, 2000);
    };

    reader.onerror = () => {
      setUploadError('Error reading file');
      setIsLoading(false);
    };

    reader.readAsText(file);
  };

  const pitcherData = useMemo(() => {
    if (!selectedPitcher) return [];
    return data.filter(pitch => pitch.Pitcher === selectedPitcher);
  }, [data, selectedPitcher]);

  const arsenalMetrics = useMemo(() => {
    if (!pitcherData.length) return {} as Record<string, PitchTypeMetrics>;
    const pitchTypes = [...new Set(pitcherData.map(pitch => pitch.AutoPitchType))];
    const metrics: Record<string, PitchTypeMetrics & { pitchScore: PitchScore }> = {};

    pitchTypes.forEach(type => {
      const pitches = pitcherData.filter(pitch => pitch.AutoPitchType === type);
      if (pitches.length > 0) {
        const averagePitch = calculateAveragePitch(pitches);
        const benchmarks = LEVEL_BENCHMARKS[selectedLevel]?.[type as PitchTypes] || {
          velocity: 0,
          spinRate: 0,
          vertBreak: 0,
          horzBreak: 0
        };

        const singlePitchMetrics = calculateSinglePitchScore(
          averagePitch,
          benchmarks,
          pitcherData,
          DEFAULT_PITCH_SCORE_PARAMS
        );

        // Velocity Separation Score Calculation
        const maxDeviationVSS = 13.5;
        const minPercentageVSS = 25;
        const velocityDifference = Math.abs(averagePitch.RelSpeed - benchmarks.velocity);
        let velocitySeparationScore;
        if (velocityDifference <= maxDeviationVSS) {
          velocitySeparationScore = 100 * Math.pow((minPercentageVSS / 100), (velocityDifference / maxDeviationVSS));
        } else {
          velocitySeparationScore = 0;
        }

        // Movement Score Calculation
        const scalingFactor = 3;
        const verticalBreakDiff = averagePitch.VertBreak - benchmarks.vertBreak;
        const horizontalBreakDiff = averagePitch.HorzBreak - benchmarks.horzBreak;
        const movementDifference = Math.sqrt(
          Math.pow(verticalBreakDiff, 2) + Math.pow(horizontalBreakDiff, 2)
        );
        const referenceMovementDifference = Math.sqrt(
          Math.pow(benchmarks.vertBreak, 2) + Math.pow(benchmarks.horzBreak, 2)
        );
        let movementScore;
        if (referenceMovementDifference !== 0) {
          movementScore = Math.min(
            ((referenceMovementDifference / movementDifference) * 100) + scalingFactor,
            100
          );
        } else {
          movementScore = 0;
        }

        // Release Point Score Calculation
        const avgRelSide = calculateAverage(pitcherData.map(p => p.RelSide));
        const avgRelHeight = calculateAverage(pitcherData.map(p => p.RelHeight));
        const relSideDiff = Math.abs(averagePitch.RelSide - avgRelSide);
        const relHeightDiff = Math.abs(averagePitch.RelHeight - avgRelHeight);
        const penaltyFactor = 1;
        let releasePointScore = Math.min(
          Math.max((1 - ((relSideDiff + relHeightDiff) * penaltyFactor)) * 100, 0),
          100
        );

        // Single Pitch Arsenal Score Calculation
        const singlePitchArsenalScore =
          velocitySeparationScore * 0.4 +
          movementScore * 0.3 +
          releasePointScore * 0.3;

        metrics[type] = {
          avgVelocity: averagePitch.RelSpeed,
          avgSpinRate: averagePitch.SpinRate,
          avgVertBreak: averagePitch.VertBreak,
          avgHorzBreak: averagePitch.HorzBreak,
          pitchScore: singlePitchMetrics,
          velocitySeparationScore,
          movementScore,
          releasePointScore,
          singlePitchArsenalScore
        };
      }
    });
    return metrics;
  }, [pitcherData, selectedLevel]);

  const arsenalScore: number = useMemo(() => {
    if (!arsenalMetrics) return 0;
    const scores = Object.values(arsenalMetrics).map(
      (metrics) => metrics.singlePitchArsenalScore || 0
    );
    if (scores.length === 0) return 0;
    return scores.reduce((sum, score) => sum + score, 0) / scores.length;
  }, [arsenalMetrics]);

  const getMetricColor = (
    value: number,
    benchmark: number,
    type: 'higher-better' | 'lower-better' = 'higher-better'
  ): string => {
    if (!value || !benchmark) return 'text-gray-600';
    const percentage = (value / benchmark) * 100;
    if (type === 'higher-better') {
      if (percentage >= 110) return 'text-green-600';
      if (percentage >= 90) return 'text-yellow-600';
      return 'text-red-600';
    } else {
      if (percentage <= 90) return 'text-green-600';
      if (percentage <= 110) return 'text-yellow-600';
      return 'text-red-600';
    }
  };

  const arsenalComparisonData = useMemo(() => {
    if (!Object.keys(arsenalMetrics).length) return [];
    return Object.entries(arsenalMetrics).map(([type, metrics]) => ({
      name: type,
      velocity: metrics.avgVelocity,
      spinRate: metrics.avgSpinRate / 100,
      verticalBreak: metrics.avgVertBreak,
      horizontalBreak: metrics.avgHorzBreak
    }));
  }, [arsenalMetrics]);

  const calculateMovementDomains = (data: PitchData[], zoom: number) => {
    if (!data.length) return { x: [-20, 20], y: [-20, 20] };
    const padding = 2;
    const centerX = (Math.max(...data.map(d => d.HorzBreak)) + Math.min(...data.map(d => d.HorzBreak))) / 2;
    const centerY = (Math.max(...data.map(d => d.VertBreak)) + Math.min(...data.map(d => d.VertBreak))) / 2;
    const rangeX = (Math.max(...data.map(d => d.HorzBreak)) - Math.min(...data.map(d => d.HorzBreak)) + padding * 2) / zoom;
    const rangeY = (Math.max(...data.map(d => d.VertBreak)) - Math.min(...data.map(d => d.VertBreak)) + padding * 2) / zoom;
    return {
      x: [centerX - rangeX / 2, centerX + rangeX / 2],
      y: [centerY - rangeY / 2, centerY + rangeY / 2]
    };
  };

  const calculateReleaseDomains = (data: PitchData[], zoom: number) => {
    if (!data.length) return { x: [0, 4], y: [0, 8] };
    const padding = 0.2;
    const centerX = (Math.max(...data.map(d => d.RelSide)) + Math.min(...data.map(d => d.RelSide))) / 2;
    const centerY = (Math.max(...data.map(d => d.RelHeight)) + Math.min(...data.map(d => d.RelHeight))) / 2;
    const rangeX = (Math.max(...data.map(d => d.RelSide)) - Math.min(...data.map(d => d.RelSide)) + padding * 2) / zoom;
    const rangeY = (Math.max(...data.map(d => d.RelHeight)) - Math.min(...data.map(d => d.RelHeight)) + padding * 2) / zoom;
    return {
      x: [Math.max(0, centerX - rangeX / 2), centerX + rangeX / 2],
      y: [Math.max(0, centerY - rangeY / 2), centerY + rangeY / 2]
    };
  };

  const ZoomControls = ({
    onZoomIn,
    onZoomOut,
    onReset
  }: {
    onZoomIn: () => void;
    onZoomOut: () => void;
    onReset: () => void;
  }) => (
    <div className="absolute top-2 right-2 flex gap-1">
      <Button
        variant="outline"
        size="sm"
        onClick={onZoomIn}
        className="h-8 w-8 p-0 bg-white/80 text-black border-black/20"
      >
        +
      </Button>
      <Button
        variant="outline"
        size="sm"
        onClick={onZoomOut}
        className="h-8 w-8 p-0 bg-white/80 text-black border-black/20"
      >
        -
      </Button>
      <Button
        variant="outline"
        size="sm"
        onClick={onReset}
        className="h-8 w-8 p-0 bg-white/80 text-black border-black/20"
      >
        ↺
      </Button>
    </div>
  );

  const { x: movementXDomain, y: movementYDomain } = useMemo(
    () => calculateMovementDomains(pitcherData, movementZoom),
    [pitcherData, movementZoom]
  );

  const { x: releaseXDomain, y: releaseYDomain } = useMemo(
    () => calculateReleaseDomains(pitcherData, releaseZoom),
    [pitcherData, releaseZoom]
  );

  return (
    <div className="min-h-screen w-full" style={{ background: 'linear-gradient(135deg, #0f0f23 0%, #1a1a2e 50%, #16213e 100%)' }}>
      {isLoading && <LoadingScreen />}
      <div className="w-full max-w-7xl mx-auto p-4 space-y-4">
        <Card className="bg-[rgba(15,15,35,0.8)] backdrop-blur-sm border-0 rounded-lg transform translate-y-0 shadow-[0_4px_16px_rgba(229,90,43,0.15)] hover:shadow-[0_6px_24px_rgba(229,90,43,0.25)] hover:-translate-y-1 transition-all duration-300">
          <CardContent className="p-6">
            <div className="flex flex-col gap-4">
              <div className="flex items-center gap-4">
                <input
                  ref={fileInputRef}
                  type="file"
                  accept=".csv"
                  onChange={handleFileUpload}
                  className="hidden"
                />
                <Button
                  onClick={handleUploadClick}
                  variant="outline"
                  className="bg-[#e55a2b] hover:bg-[#ff8c5a] text-white border border-[#e55a2b] shadow-lg shadow-[#e55a2b]/20 flex items-center gap-2 transition-all duration-300"
                >
                  <Upload className="w-4 h-4" />
                  Upload CSV
                </Button>
                {data.length > 0 && (
                  <span className="text-sm text-white">
                    {data.length} pitches loaded
                  </span>
                )}
              </div>
              {uploadError && (
                <p className="text-sm text-red-500">{uploadError}</p>
              )}
            </div>
          </CardContent>
        </Card>

        {data.length > 0 && (
          <>
            <div className="flex gap-4">
              <Select value={selectedPitcher} onValueChange={setSelectedPitcher}>
                <SelectTrigger className="w-[200px] bg-[rgba(15,15,35,0.8)] text-white border border-[#e55a2b] shadow-lg shadow-[#e55a2b]/20">
                  <SelectValue placeholder="Select Pitcher" />
                </SelectTrigger>
                <SelectContent className="bg-[rgba(15,15,35,0.8)] text-white border border-[#e55a2b] shadow-lg">
                  {pitchers.map(pitcher => (
                    <SelectItem key={pitcher} value={pitcher}>
                      {pitcher}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>

              <Select
                value={selectedLevel}
                onValueChange={(value) => setSelectedLevel(value as Level)}
              >
                <SelectTrigger className="w-[200px] bg-[rgba(15,15,35,0.8)] text-white border border-[#e55a2b] shadow-lg shadow-[#e55a2b]/20">
                  <SelectValue placeholder="Select Level" />
                </SelectTrigger>
                <SelectContent className="bg-[rgba(15,15,35,0.8)] text-white border border-[#e55a2b] shadow-lg">
                  <SelectItem value="Professional">Professional</SelectItem>
                  <SelectItem value="College">College</SelectItem>
                  <SelectItem value="High School">High School</SelectItem>
                </SelectContent>
              </Select>

              <Button
                onClick={() =>
                  setViewMode(viewMode === 'player' ? 'coach' : 'player')
                }
                variant="outline"
                className="bg-[rgba(15,15,35,0.8)] text-white border border-[#e55a2b] shadow-lg shadow-[#e55a2b]/20 hover:shadow-[#e55a2b]/40 transition-all duration-300"
              >
                {viewMode === 'player'
                  ? 'Switch to Coach View'
                  : 'Switch to Player View'}
              </Button>

              <Button
                onClick={() => setIsPdfBuilderOpen(true)}
                variant="outline"
                className="bg-[rgba(15,15,35,0.8)] text-white border border-[#e55a2b] shadow-lg shadow-[#e55a2b]/20 hover:shadow-[#e55a2b]/40 transition-all duration-300 flex items-center gap-2"
              >
                <FileText className="w-4 h-4" /> Build Report
              </Button>

              <Button
                onClick={() => setShowAdmin(!showAdmin)}
                variant="outline"
                className="bg-[rgba(15,15,35,0.8)] text-white border border-[#e55a2b] shadow-lg shadow-[#e55a2b]/20 hover:shadow-[#e55a2b]/40 transition-all duration-300"
              >
                {showAdmin ? 'Hide Admin' : 'Show Admin'}
              </Button>
            </div>

            {viewMode === 'player' ? (
              selectedPitcher && arsenalComparisonData.length > 0 && (
                <>
                  <div className="flex justify-end mb-4">
                    <Button
                      onClick={() =>
                        setShowEventTimeline(!showEventTimeline)
                      }
                      variant="outline"
                      className="bg-[rgba(15,15,35,0.8)] text-white border border-[#e55a2b] shadow-lg shadow-[#e55a2b]/20 hover:shadow-[#e55a2b]/40 transition-all duration-300"
                    >
                      {showEventTimeline
                        ? 'Hide Event Timeline'
                        : 'Show Event Timeline'}
                    </Button>
                  </div>

                  {showEventTimeline && (
                    <>
                      <Card className="bg-white backdrop-blur shadow-lg mt-4 border border-white">
                        <CardHeader>
                          <CardTitle className="text-black">
                            Event Timeline
                          </CardTitle>
                        </CardHeader>
                        <CardContent className="pt-6 text-black">
                          <div className="relative py-8">
                            <div className="absolute left-0 top-1/2 w-full h-0.5 bg-gray-200"></div>
                            <div className="relative flex justify-between px-4">
                              {pitcherData
                                .filter(
                                  (pitch, index, self) =>
                                    index ===
                                    self.findIndex((p) => p.EventID === pitch.EventID)
                                )
                                .sort((a, b) =>
                                  (a.EventDate || '').localeCompare(b.EventDate || '')
                                )
                                .map((event, index) => (
                                  <div key={index} className="flex flex-col items-center w-32">
                                    <div
                                      onClick={() => setSelectedEvent(event.EventID || null)}
                                      className={`w-5 h-5 rounded-full border-2 border-white shadow-md ${
                                        event.EventType?.includes('Baseline')
                                          ? 'bg-blue-500 hover:bg-blue-600'
                                          : 'bg-green-500 hover:bg-green-600'
                                      } ${
                                        event.EventID === selectedEvent
                                          ? 'ring-2 ring-offset-2 ring-black'
                                          : ''
                                      } z-10 cursor-pointer transition-colors duration-200`}
                                    ></div>
                                    <div className="mt-2 text-sm font-medium text-center w-full">
                                      <div className="font-semibold truncate">{event.EventType}</div>
                                      <div className="text-xs text-black-500">{event.EventDate}</div>
                                      <div className="text-xs text-black-400">{event.EventLocation}</div>
                                    </div>
                                  </div>
                                ))}
                            </div>
                          </div>
                        </CardContent>
                      </Card>

                      {selectedEvent && (
                        <Card className="bg-white/80 backdrop-blur shadow-lg mt-4">
                          <CardContent className="pt-6">
                            <div className="space-y-6">
                              <div className="grid grid-cols-2 gap-4">
                                {pitcherData
                                  .filter(pitch => pitch.EventID === selectedEvent)
                                  .slice(0, 1)
                                  .map((event, index) => (
                                    <React.Fragment key={index}>
                                      <div>
                                        <h3 className="font-semibold text-lg mb-2">
                                          {event.EventType}
                                        </h3>
                                        <div className="space-y-1">
                                          <p className="text-sm">
                                            <span className="font-medium">Date:</span> {event.EventDate}
                                          </p>
                                          <p className="text-sm">
                                            <span className="font-medium">Location:</span> {event.EventLocation}
                                          </p>
                                          <p className="text-sm">
                                            <span className="font-medium">Session ID:</span> {event.SessionID}
                                          </p>
                                        </div>
                                      </div>
                                      <div className="flex flex-col items-end justify-between">
                                        <ScoreGauge
                                          score={calculateEventArsenalScore(
                                            pitcherData.filter(pitch => pitch.EventID === selectedEvent)
                                          )}
                                          size="md"
                                          label="Event Arsenal Score"
                                        />
                                        <Button
                                          onClick={() => setSelectedEvent(null)}
                                          variant="outline"
                                          className="bg-white/80 text-black border-black/20"
                                        >
                                          Close Details
                                        </Button>
                                      </div>
                                    </React.Fragment>
                                  ))}
                              </div>
                              <div className="h-[300px] w-full">
                                <ResponsiveContainer>
                                  <BarChart
                                    data={calculateEventPitchMetrics(
                                      pitcherData.filter(pitch => pitch.EventID === selectedEvent),
                                      selectedLevel
                                    )}
                                    margin={commonChartMargin}
                                  >
                                    <CartesianGrid strokeDasharray="3 3" />
                                    <XAxis dataKey="pitchType" />
                                    <YAxis domain={[0, 100]} />
                                    <Tooltip />
                                    <Legend />
                                    <Bar dataKey="score" fill="#8884d8" name="Pitch Score" />
                                  </BarChart>
                                </ResponsiveContainer>
                              </div>
                            </div>
                          </CardContent>
                        </Card>
                      )}
                    </>
                  )}

                  <Button
                    onClick={() => {
                      const { setStorageData, STORAGE_KEYS } = require('@/lib/storage');
                      setStorageData(STORAGE_KEYS.PITCHING_DATA, data);
                      setStorageData(STORAGE_KEYS.SELECTED_PITCHER, selectedPitcher);
                      setStorageData(STORAGE_KEYS.SELECTED_LEVEL, selectedLevel);
                      router.push(
                        `/pitching-trend-analysis?pitcher=${encodeURIComponent(
                          selectedPitcher
                        )}&selectedLevel=${selectedLevel}`
                      );
                    }}
                    variant="outline"
                    className="bg-[rgba(15,15,35,0.8)] text-white border border-[#e55a2b] shadow-lg shadow-[#e55a2b]/20 hover:shadow-[#e55a2b]/40 transition-all duration-300"
                  >
                    View Trend Analysis
                  </Button>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <Card className="bg-[rgba(15,15,35,0.8)] backdrop-blur-sm border-0 rounded-lg transform translate-y-0 shadow-[0_4px_16px_rgba(229,90,43,0.15)] hover:shadow-[0_6px_24px_rgba(229,90,43,0.25)] hover:-translate-y-1 transition-all duration-300 h-[355px]">
                      <CardHeader>
                        <div className="flex justify-between items-center">
                          <CardTitle className="text-white">
                            {Object.keys(arsenalMetrics).length} Pitch Overview
                          </CardTitle>
                          <div className="flex gap-2">
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() =>
                                setCurrentPitchIndex(prev =>
                                  prev === 0 ? Object.keys(arsenalMetrics).length - 1 : prev - 1
                                )
                              }
                              className="bg-[rgba(15,15,35,0.8)] text-white border border-[#e55a2b] shadow-lg shadow-[#e55a2b]/20 h-8 w-8 p-0"
                            >
                              ←
                            </Button>
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() =>
                                setCurrentPitchIndex(prev =>
                                  prev === Object.keys(arsenalMetrics).length - 1 ? 0 : prev + 1
                                )
                              }
                              className="bg-[rgba(15,15,35,0.8)] text-white border border-[#e55a2b] shadow-lg shadow-[#e55a2b]/20 h-8 w-8 p-0"
                            >
                              →
                            </Button>
                          </div>
                        </div>
                      </CardHeader>
                      <CardContent>
                        <div className="flex flex-col items-center">
                          {Object.entries(arsenalMetrics)[currentPitchIndex] && (
                            <div className="flex flex-col items-center">
                              <ScoreGauge
                                score={
                                  Object.entries(arsenalMetrics)[currentPitchIndex][1].pitchScore
                                    ?.totalScore || 0
                                }
                                size="lg"
                                label="Pitch Details"
                              />
                              <h3 className="font-semibold text-white text-xl mt-2">
                                {Object.entries(arsenalMetrics)[currentPitchIndex][0]}
                              </h3>
                              <div className="mt-4 grid grid-cols-2 gap-x-8 gap-y-3">
                                <div>
                                  <span className="font-medium text-gray-300">Velocity: </span>
                                  <span
                                    className={getMetricColor(
                                      Object.entries(arsenalMetrics)[currentPitchIndex][1]
                                        .avgVelocity,
                                      LEVEL_BENCHMARKS[selectedLevel]?.[
                                        Object.entries(arsenalMetrics)[currentPitchIndex][0] as PitchTypes
                                      ]?.velocity || 0
                                    )}
                                  >
                                    {Object.entries(arsenalMetrics)[currentPitchIndex][1].avgVelocity.toFixed(
                                      1
                                    )}{' '}
                                    mph
                                  </span>
                                </div>
                                <div>
                                  <span className="font-medium text-gray-300">Spin Rate: </span>
                                  <span
                                    className={getMetricColor(
                                      Object.entries(arsenalMetrics)[currentPitchIndex][1]
                                        .avgSpinRate,
                                      LEVEL_BENCHMARKS[selectedLevel]?.[
                                        Object.entries(arsenalMetrics)[currentPitchIndex][0] as PitchTypes
                                      ]?.spinRate || 0,
                                      'higher-better'
                                    )}
                                  >
                                    {Object.entries(arsenalMetrics)[currentPitchIndex][1].avgSpinRate.toFixed(
                                      0
                                    )}{' '}
                                    rpm
                                  </span>
                                </div>
                                <div>
                                  <span className="font-medium text-gray-300">Vertical Break: </span>
                                  <span
                                    className={getMetricColor(
                                      Object.entries(arsenalMetrics)[currentPitchIndex][1]
                                        .avgVertBreak,
                                      LEVEL_BENCHMARKS[selectedLevel]?.[
                                        Object.entries(arsenalMetrics)[currentPitchIndex][0] as PitchTypes
                                      ]?.vertBreak || 0,
                                      'higher-better'
                                    )}
                                  >
                                    {Object.entries(arsenalMetrics)[currentPitchIndex][1].avgVertBreak.toFixed(
                                      1
                                    )}{' '}
                                    in
                                  </span>
                                </div>
                                <div>
                                  <span className="font-medium text-gray-300">Horizontal Break: </span>
                                  <span
                                    className={getMetricColor(
                                      Object.entries(arsenalMetrics)[currentPitchIndex][1]
                                        .avgHorzBreak,
                                      LEVEL_BENCHMARKS[selectedLevel]?.[
                                        Object.entries(arsenalMetrics)[currentPitchIndex][0] as PitchTypes
                                      ]?.horzBreak || 0,
                                      'lower-better'
                                    )}
                                  >
                                    {Object.entries(arsenalMetrics)[currentPitchIndex][1].avgHorzBreak?.toFixed(1) ||
                                      'N/A'}{' '}
                                    in
                                  </span>
                                </div>
                              </div>
                            </div>
                          )}
                        </div>
                      </CardContent>
                    </Card>

                    <Card className="bg-[rgba(15,15,35,0.8)] backdrop-blur-sm border-0 rounded-lg transform translate-y-0 shadow-[0_4px_16px_rgba(229,90,43,0.15)] hover:shadow-[0_6px_24px_rgba(229,90,43,0.25)] hover:-translate-y-1 transition-all duration-300 h-[355px]">
                      <CardContent className="relative pt-12">
                        <div className="flex items-center justify-center h-full">
                          <div className="flex-shrink-0">
                            <ScoreGauge score={arsenalScore} size="lg" label="Arsenal Score" />
                          </div>
                          <div className="flex flex-col ml-4">
                            <Button
                              onClick={() => setShowArsenalDetails(!showArsenalDetails)}
                              variant="outline"
                              className="absolute top-2 right-2 bg-[rgba(15,15,35,0.8)] text-white border border-[#e55a2b] shadow-lg shadow-[#e55a2b]/20 text-sm px-3 py-1"
                            >
                              {showArsenalDetails ? 'Hide Details' : 'Show Details'}
                            </Button>
                            {showArsenalDetails && (
                              <div className="text-sm">
                                {Object.entries(arsenalMetrics).map(([type, metrics]) => (
                                  <div key={type} className="mb-1">
                                    <span className="font-semibold text-white">{type}:</span>
                                    <span className="text-gray-300 ml-1">
                                      {metrics.singlePitchArsenalScore?.toFixed(1)}
                                    </span>
                                  </div>
                                ))}
                              </div>
                            )}
                          </div>
                        </div>
                      </CardContent>
                    </Card>

                    <Card className="bg-[rgba(15,15,35,0.8)] backdrop-blur-sm border-0 rounded-lg transform translate-y-0 shadow-[0_4px_16px_rgba(229,90,43,0.15)] hover:shadow-[0_6px_24px_rgba(229,90,43,0.25)] hover:-translate-y-1 transition-all duration-300">
                      <CardHeader>
                        <CardTitle className="text-white">Movement Profile</CardTitle>
                      </CardHeader>
                      <CardContent>
                        <div className="w-full h-[400px] relative">
                          <ZoomControls
                            onZoomIn={() => setMovementZoom(prev => prev * 1.2)}
                            onZoomOut={() => setMovementZoom(prev => prev * 0.8)}
                            onReset={() => setMovementZoom(1)}
                          />
                          <ResponsiveContainer>
                            <ScatterChart margin={commonChartMargin}>
                              <CartesianGrid strokeDasharray="3 3" stroke="#cccccc" />
                              <XAxis
                                type="number"
                                dataKey="HorzBreak"
                                domain={movementXDomain}
                                tickCount={7}
                                stroke="#000000"
                                label={{
                                  value: 'Horizontal Break (inches)',
                                  position: 'bottom',
                                  offset: 20,
                                  style: { textAnchor: 'middle' }
                                }}
                              />
                              <YAxis
                                type="number"
                                dataKey="VertBreak"
                                domain={movementYDomain}
                                tickCount={7}
                                stroke="#000000"
                                label={{
                                  value: 'Vertical Break (inches)',
                                  angle: -90,
                                  position: 'left',
                                  offset: 20,
                                  style: { textAnchor: 'middle' }
                                }}
                                tickFormatter={(value) => value.toFixed(2)}
                              />
                              <ReferenceLine x={0} stroke="#000000" strokeWidth={1.5} />
                              <ReferenceLine y={0} stroke="#000000" strokeWidth={1.5} />
                              {Array.from(new Set(pitcherData.map(pitch => pitch.AutoPitchType))).map((pitchType) => (
                                <Scatter
                                  key={pitchType}
                                  name={pitchType}
                                  data={pitcherData.filter(pitch => pitch.AutoPitchType === pitchType)}
                                  fill={getPitchTypeColor(pitchType)}
                                  opacity={
                                    Object.entries(arsenalMetrics)[currentPitchIndex][0] === pitchType
                                      ? 1
                                      : 0.2
                                  }
                                  stroke={
                                    Object.entries(arsenalMetrics)[currentPitchIndex][0] === pitchType
                                      ? 'black'
                                      : 'none'
                                  }
                                  strokeWidth={
                                    Object.entries(arsenalMetrics)[currentPitchIndex][0] === pitchType
                                      ? 1
                                      : 0
                                  }
                                  shape="circle"
                                  legendType="circle"
                                />
                              ))}
                              <Tooltip
                                cursor={false}
                                contentStyle={{
                                  backgroundColor: 'white',
                                  border: '1px solid #cccccc',
                                  borderRadius: '4px',
                                  padding: '8px'
                                }}
                                content={({ payload }) => {
                                  if (payload && payload[0]) {
                                    const data = payload[0].payload;
                                    return (
                                      <div className="bg-white p-2 rounded shadow">
                                        <p className="font-bold">{data.AutoPitchType}</p>
                                        <p>Horizontal: {data.HorzBreak.toFixed(1)} in</p>
                                        <p>Vertical: {data.VertBreak.toFixed(1)} in</p>
                                        <p>Velocity: {data.RelSpeed.toFixed(1)} mph</p>
                                      </div>
                                    );
                                  }
                                  return null;
                                }}
                              />
                              <Legend verticalAlign="top" height={36} />
                            </ScatterChart>
                          </ResponsiveContainer>
                        </div>
                      </CardContent>
                    </Card>

                    <Card className="bg-[rgba(15,15,35,0.8)] backdrop-blur-sm border-0 rounded-lg transform translate-y-0 shadow-[0_4px_16px_rgba(229,90,43,0.15)] hover:shadow-[0_6px_24px_rgba(229,90,43,0.25)] hover:-translate-y-1 transition-all duration-300">
                      <CardHeader>
                        <CardTitle className="text-white">Release Point Consistency</CardTitle>
                      </CardHeader>
                      <CardContent>
                        <div className="w-full h-[400px] relative">
                          <ZoomControls
                            onZoomIn={() => setReleaseZoom(prev => prev * 1.2)}
                            onZoomOut={() => setReleaseZoom(prev => prev * 0.8)}
                            onReset={() => setReleaseZoom(1)}
                          />
                          <ResponsiveContainer width="100%" height="100%">
                            <ScatterChart margin={commonChartMargin}>
                              <CartesianGrid strokeDasharray="3 3" stroke="#000000" opacity={0.15} />
                              <XAxis
                                type="number"
                                dataKey="RelSide"
                                domain={releaseXDomain}
                                tickCount={5}
                                stroke="#000000"
                                label={{
                                  value: 'Release Side (ft)',
                                  position: 'bottom',
                                  offset: 20,
                                  style: { textAnchor: 'middle' }
                                }}
                              />
                              <YAxis
                                type="number"
                                dataKey="RelHeight"
                                domain={releaseYDomain}
                                tickCount={5}
                                stroke="#000000"
                                label={{
                                  value: 'Release Height (ft)',
                                  angle: -90,
                                  position: 'left',
                                  offset: 20,
                                  style: { textAnchor: 'middle' }
                                }}
                                tickFormatter={(value) => value.toFixed(2)}
                              />
                              <Tooltip
                                cursor={false}
                                content={({ payload }) => {
                                  if (payload && payload[0]) {
                                    const data = payload[0].payload;
                                    return (
                                      <div className="bg-white p-2 rounded shadow">
                                        <p className="font-bold">{data.AutoPitchType}</p>
                                        <p>Side: {data.RelSide.toFixed(2)} ft</p>
                                        <p>Height: {data.RelHeight.toFixed(2)} ft</p>
                                        <p>Extension: {data.Extension.toFixed(2)} ft</p>
                                      </div>
                                    );
                                  }
                                  return null;
                                }}
                              />
                              {Array.from(new Set(pitcherData.map(pitch => pitch.AutoPitchType))).map((pitchType) => (
                                <Scatter
                                  key={pitchType}
                                  name={pitchType}
                                  data={pitcherData.filter(pitch => pitch.AutoPitchType === pitchType)}
                                  fill={getPitchTypeColor(pitchType)}
                                  opacity={
                                    Object.entries(arsenalMetrics)[currentPitchIndex][0] === pitchType
                                      ? 1
                                      : 0.2
                                  }
                                  stroke={
                                    Object.entries(arsenalMetrics)[currentPitchIndex][0] === pitchType
                                      ? 'black'
                                      : 'none'
                                  }
                                  strokeWidth={
                                    Object.entries(arsenalMetrics)[currentPitchIndex][0] === pitchType
                                      ? 1
                                      : 0
                                  }
                                  shape="circle"
                                  legendType="circle"
                                />
                              ))}
                              <Legend verticalAlign="top" height={36} />
                            </ScatterChart>
                          </ResponsiveContainer>
                        </div>
                      </CardContent>
                    </Card>

                    {showAdmin && (
                      <Card className="col-span-2 bg-white/100 backdrop-blur shadow-lg">
                        <CardHeader>
                          <CardTitle className="text-black font-bold">Admin Panel - Score Calculations</CardTitle>
                        </CardHeader>
                        <CardContent>
                          {/* Admin panel content... */}
                        </CardContent>
                      </Card>
                    )}
                  </div>
                </>
              )
            ) : (
              <CoachDashboard
                data={data}
                selectedLevel={selectedLevel}
                calculatePitcherMetrics={(pitcherData) => {
                  // Using the existing arsenal calculations logic:
                  const metrics = Object.entries(PITCH_TYPE_WEIGHTS).reduce((acc, [type]) => {
                    const pitches = pitcherData.filter(p => p.AutoPitchType === type);
                    if (pitches.length === 0) return acc;
                    const avgPitch = calculateAveragePitch(pitches);
                    const benchmarks = LEVEL_BENCHMARKS[selectedLevel]?.[type as PitchTypes];
                    if (!benchmarks) return acc;
                    const score = calculateSinglePitchScore(avgPitch, benchmarks, pitcherData);
                    const maxDeviationVSS = 13.5;
                    const minPercentageVSS = 25;
                    const velocityDifference = Math.abs(avgPitch.RelSpeed - benchmarks.velocity);
                    let velocitySeparationScore;
                    if (velocityDifference <= maxDeviationVSS) {
                      velocitySeparationScore = 100 * Math.pow((minPercentageVSS / 100), (velocityDifference / maxDeviationVSS));
                    } else {
                      velocitySeparationScore = 0;
                    }
                    const scalingFactor = 3;
                    const verticalBreakDiff = avgPitch.VertBreak - benchmarks.vertBreak;
                    const horizontalBreakDiff = avgPitch.HorzBreak - benchmarks.horzBreak;
                    const movementDifference = Math.sqrt(
                      Math.pow(verticalBreakDiff, 2) + Math.pow(horizontalBreakDiff, 2)
                    );
                    const referenceMovementDifference = Math.sqrt(
                      Math.pow(benchmarks.vertBreak, 2) + Math.pow(benchmarks.horzBreak, 2)
                    );
                    let movementScore;
                    if (referenceMovementDifference !== 0) {
                      movementScore = Math.min(
                        ((referenceMovementDifference / movementDifference) * 100) + scalingFactor,
                        100
                      );
                    } else {
                      movementScore = 0;
                    }
                    const avgRelSide = calculateAverage(pitcherData.map(p => p.RelSide));
                    const avgRelHeight = calculateAverage(pitcherData.map(p => p.RelHeight));
                    const relSideDiff = Math.abs(avgPitch.RelSide - avgRelSide);
                    const relHeightDiff = Math.abs(avgPitch.RelHeight - avgRelHeight);
                    const penaltyFactor = 1;
                    let releasePointScore = Math.min(
                      Math.max((1 - ((relSideDiff + relHeightDiff) * penaltyFactor)) * 100, 0),
                      100
                    );
                    const singlePitchArsenalScore =
                      velocitySeparationScore * 0.4 +
                      movementScore * 0.3 +
                      releasePointScore * 0.3;
                    acc[type] = {
                      avgPitch,
                      score,
                      velocitySeparationScore,
                      movementScore,
                      releasePointScore,
                      singlePitchArsenalScore
                    };
                    return acc;
                  }, {} as Record<string, any>);

                  const scores = Object.values(metrics).map(
                    (metrics: any) => metrics.singlePitchArsenalScore || 0
                  );
                  const arsenalScore = scores.length > 0
                    ? scores.reduce((sum, score) => sum + score, 0) / scores.length
                    : 0;

                  return {
                    arsenalScore,
                    arsenalMetrics: metrics
                  };
                }}
              />
            )}
          </>
        )}
      </div>

      {/* AI Chat Widget */}
      {selectedPitcher && (
        <ChatWidget
          playerId={selectedPitcher}
          playerName={selectedPitcher}
          dataType="pitching"
          level={selectedLevel}
        />
      )}
    </div>
  );
};

// -----------------------------
// Event Metrics Calculations
// -----------------------------
export const calculateEventPitchMetrics = (
  eventPitches: PitchData[],
  level: Level
) => {
  const pitchTypes = [...new Set(eventPitches.map(pitch => pitch.AutoPitchType))];
  return pitchTypes.map(type => {
    const pitchesOfType = eventPitches.filter(pitch => pitch.AutoPitchType === type);
    const avgPitch = calculateAveragePitch(pitchesOfType);
    const benchmarks = LEVEL_BENCHMARKS[level]?.[type as PitchTypes] || {
      velocity: 0,
      spinRate: 0,
      vertBreak: 0,
      horzBreak: 0
    };
    const score = calculateSinglePitchScore(avgPitch, benchmarks, eventPitches);
    return {
      pitchType: type,
      score: score.totalScore
    };
  });
};

const calculateEventArsenalScore = (eventPitches: PitchData[]): number => {
  if (eventPitches.length === 0) return 0;
  return calculateArsenalScore(eventPitches);
};

const getPitchTypeColor = (pitchType: string) => {
  const colorMap: { [key: string]: string } = {
    FF: '#e63946',
    SL: '#1d3557',
    CH: '#2a9d8f',
    CU: '#6b705c',
    Sinker: '#f4a261',
    Sweeper: '#bc6c25',
    Splitter: '#588157'
  };
  return colorMap[pitchType] || '#000000';
};

export default PitchingMetricsDashboard;
