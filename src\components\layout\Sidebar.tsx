"use client";

import React, { useState } from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { 
  BarChart3, 
  Users, 
  User, 
  TrendingUp, 
  FileText, 
  Settings,
  Menu,
  X,
  Target,
  CircleDot,
  Trophy,
  UserCheck
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';

interface SidebarProps {
  className?: string;
}

const navigation = [
  {
    name: 'Facility Dashboard',
    href: '/dashboard',
    icon: BarChart3,
    description: 'High-level overview of all players'
  },
  {
    name: 'Player Dashboard',
    href: '/player-dashboard',
    icon: User,
    description: 'Individual player analytics'
  },
  {
    name: 'Coach Dashboard',
    href: '/coach-dashboard',
    icon: UserCheck,
    description: 'Coach impact and training effectiveness'
  },
  {
    name: 'Compare Players',
    href: '/compare',
    icon: Users,
    description: 'Dynamic player comparison'
  },
  {
    name: 'Reports',
    href: '/reports',
    icon: FileText,
    description: 'Customizable report generation'
  },
  {
    name: 'Hitting Analysis',
    href: '/player-dashboard/hitting-analysis',
    icon: Target,
    description: 'Advanced hitting visualization'
  },
  {
    name: 'Pitching Analysis',
    href: '/pitching-trend-analysis',
    icon: CircleDot,
    description: 'Pitching performance trends'
  },
  {
    name: 'Admin',
    href: '/admin',
    icon: Settings,
    description: 'System administration'
  }
];

export default function Sidebar({ className }: SidebarProps) {
  const [isCollapsed, setIsCollapsed] = useState(false);
  const pathname = usePathname();

  return (
    <div className={cn(
      "flex flex-col bg-gray-900 text-white transition-all duration-300",
      isCollapsed ? "w-16" : "w-64",
      className
    )}>
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b border-gray-700">
        {!isCollapsed && (
          <div className="flex items-center space-x-2">
            <Trophy className="h-8 w-8 text-orange-500" />
            <span className="text-xl font-bold">Baseline Edge</span>
          </div>
        )}
        <Button
          variant="ghost"
          size="icon"
          onClick={() => setIsCollapsed(!isCollapsed)}
          className="text-gray-400 hover:text-white hover:bg-gray-800"
        >
          {isCollapsed ? <Menu className="h-5 w-5" /> : <X className="h-5 w-5" />}
        </Button>
      </div>

      {/* Navigation */}
      <nav className="flex-1 px-2 py-4 space-y-1">
        {navigation.map((item) => {
          const isActive = pathname === item.href || 
            (item.href !== '/dashboard' && pathname.startsWith(item.href));
          
          return (
            <Link
              key={item.name}
              href={item.href}
              className={cn(
                "flex items-center px-2 py-2 text-sm font-medium rounded-md transition-colors",
                isActive
                  ? "bg-orange-600 text-white"
                  : "text-gray-300 hover:bg-gray-700 hover:text-white",
                isCollapsed ? "justify-center" : "justify-start"
              )}
              title={isCollapsed ? item.name : undefined}
            >
              <item.icon className={cn("h-5 w-5", !isCollapsed && "mr-3")} />
              {!isCollapsed && (
                <div className="flex flex-col">
                  <span>{item.name}</span>
                  <span className="text-xs text-gray-400">{item.description}</span>
                </div>
              )}
            </Link>
          );
        })}
      </nav>

      {/* Footer */}
      <div className="p-4 border-t border-gray-700">
        {!isCollapsed && (
          <div className="text-xs text-gray-400">
            <p>Baseline Edge v2.0</p>
            <p>Professional Analytics</p>
          </div>
        )}
      </div>
    </div>
  );
}
