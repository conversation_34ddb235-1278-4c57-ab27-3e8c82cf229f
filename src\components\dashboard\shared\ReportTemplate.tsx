import { Page, Text, View, Document, StyleSheet, Image } from '@react-pdf/renderer';

const styles = StyleSheet.create({
  container: { padding: 30, fontFamily: 'Helvetica' },
  header: { marginBottom: 20 },
  title: { fontSize: 24, textAlign: 'center' },
  section: { marginBottom: 10 },
  metricRow: { flexDirection: 'row', justifyContent: 'space-between', marginVertical: 5 },
  metricLabel: { fontSize: 14 },
  metricValue: { fontSize: 14, fontWeight: 'bold' },
  notes: { marginTop: 20, fontSize: 12, fontStyle: 'italic' },
  image: { width: 100, height: 100, marginVertical: 10 },
});

interface Metric {
  label: string;
  value: number;
  gaugeImage?: string; // Add support for gauge images
}

const ReportTemplate = ({ playerName, metrics, notes }: { playerName: string; metrics: Metric[]; notes?: string }) => (
  <Document>
    <Page style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>{playerName}'s Performance Report</Text>
      </View>

      <View>
        {metrics.map(({ label, value, gaugeImage }) => (
          <View key={label} style={styles.metricRow}>
            <Text style={styles.metricLabel}>{label}</Text>
            <Text style={styles.metricValue}>{value.toFixed(2)}</Text>
            {gaugeImage && <Image src={gaugeImage} style={styles.image} />}
          </View>
        ))}
      </View>

      {notes && (
        <View>
          <Text style={styles.notes}>Notes: {notes}</Text>
        </View>
      )}
    </Page>
  </Document>
);

export default ReportTemplate;

