import { HittingData } from '@/components/dashboard/Hitting/HittingMetricsDashboard';
import { PitchData } from '@/components/dashboard/PitchingMetricsDashboard';
import { getPlayerMetrics } from '@/server/data';
import { getStorageData, STORAGE_KEYS } from '@/lib/storage';

/**
 * Get player metrics from localStorage for AI queries
 */
export const getPlayerMetricsFromStorage = (
  playerId: string,
  level: 'Professional' | 'College' | 'High School' = 'Professional'
) => {
  try {
    // Get data from localStorage
    const hittingData: HittingData[] = getStorageData(STORAGE_KEYS.HITTING_DATA, []);
    const pitchingData: PitchData[] = getStorageData(STORAGE_KEYS.PITCHING_DATA, []);
    
    // Extract metrics using the server function
    return getPlayerMetrics(playerId, hittingData, pitchingData, level);
  } catch (error) {
    console.error('Error getting player metrics from storage:', error);
    return null;
  }
};

/**
 * Get all available players from localStorage
 */
export const getAvailablePlayersFromStorage = () => {
  try {
    const hittingData: HittingData[] = getStorageData(STORAGE_KEYS.HITTING_DATA, []);
    const pitchingData: PitchData[] = getStorageData(STORAGE_KEYS.PITCHING_DATA, []);
    
    const hitters = [...new Set(hittingData.map(h => h.Batter))];
    const pitchers = [...new Set(pitchingData.map(p => p.Pitcher))];
    
    return {
      hitters,
      pitchers,
      all: [...new Set([...hitters, ...pitchers])]
    };
  } catch (error) {
    console.error('Error getting available players from storage:', error);
    return { hitters: [], pitchers: [], all: [] };
  }
};

/**
 * Validate if a player has data for a specific query type
 */
export const validatePlayerData = (
  playerId: string,
  dataType: 'hitting' | 'pitching' | 'both' = 'both'
) => {
  try {
    const hittingData: HittingData[] = getStorageData(STORAGE_KEYS.HITTING_DATA, []);
    const pitchingData: PitchData[] = getStorageData(STORAGE_KEYS.PITCHING_DATA, []);
    
    const hasHittingData = hittingData.some(h => h.Batter === playerId);
    const hasPitchingData = pitchingData.some(p => p.Pitcher === playerId);
    
    switch (dataType) {
      case 'hitting':
        return hasHittingData;
      case 'pitching':
        return hasPitchingData;
      case 'both':
        return hasHittingData || hasPitchingData;
      default:
        return false;
    }
  } catch (error) {
    console.error('Error validating player data:', error);
    return false;
  }
};

/**
 * Format player metrics for display
 */
export const formatMetricsForDisplay = (metrics: any) => {
  if (!metrics) return 'No data available';
  
  const parts: string[] = [];
  
  if (metrics.hitting) {
    parts.push(`Hitting: ${metrics.hitting.totalHits} hits, ${metrics.hitting.avgExitVelocity} mph avg exit velocity`);
  }
  
  if (metrics.pitching) {
    parts.push(`Pitching: ${metrics.pitching.totalPitches} pitches, ${metrics.pitching.arsenalScore} arsenal score`);
  }
  
  return parts.join(' | ');
};

/**
 * Generate contextual suggestions based on available data
 */
export const generateContextualSuggestions = (playerId: string) => {
  const players = getAvailablePlayersFromStorage();
  const hasHittingData = players.hitters.includes(playerId);
  const hasPitchingData = players.pitchers.includes(playerId);
  
  const suggestions: string[] = [];
  
  if (hasHittingData) {
    suggestions.push(
      "What was my average exit velocity?",
      "Show me my hitting performance breakdown",
      "How does my launch angle compare to benchmarks?"
    );
  }
  
  if (hasPitchingData) {
    suggestions.push(
      "Analyze my pitch arsenal",
      "What's my fastest pitch velocity?",
      "Compare my pitch types by effectiveness"
    );
  }
  
  if (hasHittingData && hasPitchingData) {
    suggestions.push(
      "Compare my hitting and pitching performance",
      "What are my overall strengths and weaknesses?"
    );
  }
  
  // General suggestions
  suggestions.push(
    "How do I compare to other players at my level?",
    "What should I focus on improving?"
  );
  
  return suggestions.slice(0, 6); // Limit to 6 suggestions
};

/**
 * Check if LLaMA server is available
 */
export const checkLLaMAServerStatus = async (): Promise<boolean> => {
  try {
    const response = await fetch('/api/query', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        playerId: 'test',
        question: 'test'
      }),
    });
    
    // If we get any response (even an error), the server is reachable
    return response.status !== 0;
  } catch (error) {
    console.error('LLaMA server check failed:', error);
    return false;
  }
};

/**
 * Sanitize user input for AI queries
 */
export const sanitizeQuery = (query: string): string => {
  return query
    .trim()
    .replace(/[<>]/g, '') // Remove potential HTML tags
    .substring(0, 500); // Limit length
};

/**
 * Extract player ID from various formats
 */
export const extractPlayerId = (input: string): string => {
  // Handle various formats like "Player Name", "player-name", etc.
  return input
    .toLowerCase()
    .replace(/[^a-z0-9\s-]/g, '')
    .replace(/\s+/g, '-')
    .trim();
};
