"use client"

import React, { useState, useEffect, useMemo, useRef, ChangeEvent, useCallback } from 'react';
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Button } from '@/components/ui/button';
import { Upload, FileText } from 'lucide-react';
import { ScoreGauge } from '../PitchingMetricsDashboard';
import { Scatter<PERSON><PERSON>, Scatter, <PERSON>Axis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer } from 'recharts';
import Spray<PERSON>hart from './SprayChart';
import ContactHeatmap from '@/components/dashboard/Hitting/ContactHeatmap';
import CoachDashboard from '../shared/CoachDashboard';
import PdfBuilderModal from '../shared/PdfBuilderModal';
import HittingTrendAnalysis from './HittingTrendAnalysis';
import { classifyHitByAngle } from '../../utils/hittingUtils';
import { useFilteredData } from '@/hooks/useFilteredData';
import VelocityFilter from './VelocityFilter';
import { useRouter } from 'next/navigation';
import ChatWidget from '@/components/ai/ChatWidget';

//
// ─── TYPES AND INTERFACES ─────────────────────────────────────────────────────────
//
export interface HittingData {
  Batter: string;
  BatterSide: string;
  ExitSpeed: number;
  RelSpeed: number;
  Angle: number;
  Direction: number;
  Distance: number;
  HitSpinRate: number;
  ContactPositionY: number;
  HitSpinAxis: number;
  Level: 'Professional' | 'College' | 'High School';
  EventID?: string;
  "Event Date"?: string;
  "Event Location"?: string;
  "Event Type"?: string;
  "Session ID"?: string;
  "Next Event"?: string;
}

export interface VelocityRange {
  min: number;
  max: number;
  label: string;
  type: 'exit' | 'pitch';
}

interface QualityOfContactParams {
  weight_ev: number;
  weight_sr: number;
  weight_sa: number;
}

export interface RoomForErrorParams {
  max_spread_score: number;
  max_ev_score: number;
  max_consistency_score: number;
  max_line_drive_score: number;
  line_drive_min_angle: number;
  line_drive_max_angle: number;
  min_ev_for_line_drive: number;
  max_possible_stddev_ev?: number;
  consistency_scaling_constant?: number;
  spread_scaling_constant?: number;
  levelParams: {
    [key in 'Professional' | 'College' | 'High School']: LevelThresholds
  }
}

interface LaunchAngleParams {
  la_weight: number;
  ev_weight: number;
  la_scaling_factor: number;
  min_la_score: number;
  line_drive_bonus: number;
}

interface LevelBenchmarks {
  minOptimalEV: number;
  maxOptimalEV: number;
  belowOptimalStart: number;
  belowOptimalEnd: number;
  optimalStart: number;
  optimalEnd: number;
  aboveAverageStart: number;
  aboveAverageEnd: number;
  perfectStart: number;
  perfectEnd: number;
}

// Add to existing types
export interface LevelThresholds {
  minEffectiveEV: number;
  optimalEV: number;
  lineDriveAngles: [number, number];
  maxSpreadInches: number;
  minMissQualityForSpread: number;
}

//
// ─── THEME CONSTANTS ──────────────────────────────────────────────────────────────
//
const THEME_COLORS = {
  primary: '#e55a2b', // Softer burnt orange
  primaryHover: '#ff8c5a',
  background: 'linear-gradient(135deg, #0f0f23 0%, #1a1a2e 50%, #16213e 100%)', // Dark gradient background
  cardBackground: 'rgba(15, 15, 35, 0.8)', // Dark semi-transparent
  cardBorder: '#e55a2b', // Softer burnt orange border
  text: '#ffffff',
  textSecondary: '#a0a0a0',
  accent: '#e55a2b',
  glowColor: '#e55a2b'
} as const;

const BUTTON_VARIANTS = {
  upload: `bg-[${THEME_COLORS.primary}] hover:bg-[${THEME_COLORS.primaryHover}] text-white border border-[${THEME_COLORS.cardBorder}] shadow-lg shadow-[${THEME_COLORS.glowColor}]/20 flex items-center gap-2 transition-all duration-300`,
  outline: `bg-[${THEME_COLORS.cardBackground}] text-white border border-[${THEME_COLORS.cardBorder}] shadow-lg shadow-[${THEME_COLORS.glowColor}]/20 hover:shadow-[${THEME_COLORS.glowColor}]/40 transition-all duration-300`,
  outlineHover: `bg-[${THEME_COLORS.cardBackground}] hover:bg-[${THEME_COLORS.cardBackground}]/90 text-white border border-[${THEME_COLORS.cardBorder}] shadow-lg shadow-[${THEME_COLORS.glowColor}]/20 hover:shadow-[${THEME_COLORS.glowColor}]/40 transition-all duration-300`
} as const;

const SELECT_VARIANTS = {
  trigger: `w-[200px] bg-[${THEME_COLORS.cardBackground}] text-white border border-[${THEME_COLORS.cardBorder}] shadow-lg shadow-[${THEME_COLORS.glowColor}]/20`,
  content: `bg-[${THEME_COLORS.cardBackground}] text-white border border-[${THEME_COLORS.cardBorder}] shadow-lg`
} as const;

const CARD_VARIANTS = {
  default: `bg-[${THEME_COLORS.cardBackground}] backdrop-blur-sm border-0 rounded-lg transform translate-y-0 shadow-[0_4px_16px_rgba(229,90,43,0.15)] hover:shadow-[0_6px_24px_rgba(229,90,43,0.25)] hover:-translate-y-1 transition-all duration-300`,
  glow: `bg-[${THEME_COLORS.cardBackground}] backdrop-blur-sm border-0 rounded-lg transform translate-y-0 shadow-[0_6px_24px_rgba(229,90,43,0.2)] hover:shadow-[0_8px_32px_rgba(229,90,43,0.3)] hover:-translate-y-2 transition-all duration-300`,
  floating: `bg-[${THEME_COLORS.cardBackground}] backdrop-blur-sm border-0 rounded-lg transform translate-y-0 shadow-[0_8px_32px_rgba(229,90,43,0.25)] hover:shadow-[0_12px_48px_rgba(229,90,43,0.35)] hover:-translate-y-3 transition-all duration-500`
} as const;

//
// ─── DEFAULT CONSTANTS ──────────────────────────────────────────────────────────────
//
export const DEFAULT_VELOCITY_RANGES = {
  exit: [
    { min: 0, max: 999, label: 'All', type: 'exit' },
    { min: 80, max: 85, label: '80-85 mph', type: 'exit' },
    { min: 85, max: 90, label: '85-90 mph', type: 'exit' },
    { min: 90, max: 95, label: '90-95 mph', type: 'exit' },
    { min: 95, max: 100, label: '95-100 mph', type: 'exit' },
    { min: 100, max: 120, label: '100+ mph', type: 'exit' }
  ] as VelocityRange[],
  pitch: [
    { min: 0, max: 999, label: 'All', type: 'pitch' },
    { min: 70, max: 75, label: '70-75 mph', type: 'pitch' },
    { min: 75, max: 80, label: '75-80 mph', type: 'pitch' },
    { min: 80, max: 85, label: '80-85 mph', type: 'pitch' },
    { min: 85, max: 90, label: '85-90 mph', type: 'pitch' },
    { min: 90, max: 95, label: '90-95 mph', type: 'pitch' }
  ] as VelocityRange[]
};

export const DEFAULT_PARAMS = {
  roomForError: {
    max_spread_score: 20,
    max_ev_score: 30,
    max_consistency_score: 20,
    max_line_drive_score: 20,
    line_drive_min_angle: 10,
    line_drive_max_angle: 25,
    min_ev_for_line_drive: 90,
    max_possible_stddev_ev: 20,
    consistency_scaling_constant: 0.2876820725,
    spread_scaling_constant: 0.2876820725,
    levelParams: {
      Professional: {
        minEffectiveEV: 88,
        optimalEV: 95,
        lineDriveAngles: [10, 25] as [number, number],
        maxSpreadInches: 10,
        minMissQualityForSpread: 0.5
      },
      College: {
        minEffectiveEV: 83,
        optimalEV: 90,
        lineDriveAngles: [8, 28] as [number, number],
        maxSpreadInches: 14,
        minMissQualityForSpread: 0.4
      },
      "High School": {
        minEffectiveEV: 75,
        optimalEV: 85,
        lineDriveAngles: [5, 30] as [number, number],
        maxSpreadInches: 18,
        minMissQualityForSpread: 0.3
      }
    }
  },
  qualityOfContact: {
    weight_ev: 1,
    weight_sr: 0.0,
    weight_sa: 0.0
  },
  launchAngle: {
    la_weight: 0.7,
    ev_weight: 0.3,
    la_scaling_factor: 1,
    min_la_score: 0,
    line_drive_bonus: 20,
  }
};

export const LEVEL_BENCHMARKS: Record<string, LevelBenchmarks> = {
  Professional: {
    minOptimalEV: 92,
    maxOptimalEV: 119,
    belowOptimalStart: 0,
    belowOptimalEnd: 88.99,
    optimalStart: 92,
    optimalEnd: 94.99,
    aboveAverageStart: 95,
    aboveAverageEnd: 97.99,
    perfectStart: 100,
    perfectEnd: 120
  },
  College: {
    minOptimalEV: 85,
    maxOptimalEV: 109,
    belowOptimalStart: 0,
    belowOptimalEnd: 86.99,
    optimalStart: 88,
    optimalEnd: 89.99,
    aboveAverageStart: 90,
    aboveAverageEnd: 92.99,
    perfectStart: 93,
    perfectEnd: 120
  },
  "High School": {
    minOptimalEV: 80,
    maxOptimalEV: 95,
    belowOptimalStart: 0,
    belowOptimalEnd: 79.99,
    optimalStart: 80,
    optimalEnd: 89.99,
    aboveAverageStart: 90,
    aboveAverageEnd: 94.99,
    perfectStart: 95,
    perfectEnd: 120
  }
};

//
// ─── HELPER FUNCTIONS ──────────────────────────────────────────────────────────────
//

const calculateEVScore = (
  exitVelocity: number,
  benchmarks: LevelBenchmarks,
  level: 'Professional' | 'College' | 'High School' | undefined
): number => {
  if (!exitVelocity) return 0;
  const effectiveLevel: 'Professional' | 'College' | 'High School' =
    (level === 'Professional' || level === 'College' || level === 'High School')
      ? level
      : "College";
  const levelParams: Record<string, { decayBelow: number; bonusPerMph: number; baseScore: number }> = {
    Professional: { decayBelow: 0.045, bonusPerMph: 1.8, baseScore: 65 },
    College: { decayBelow: 0.03, bonusPerMph: 1.8, baseScore: 65 },
    "High School": { decayBelow: 0.02, bonusPerMph: 2.0, baseScore: 65 }
  };
  const params = levelParams[effectiveLevel];
  if (exitVelocity < benchmarks.optimalStart) {
    const score = params.baseScore * Math.exp(-params.decayBelow * (benchmarks.optimalStart - exitVelocity));
    return Math.max(40, score);
  } else {
    const bonus = Math.min(30, (exitVelocity - benchmarks.optimalStart) * params.bonusPerMph);
    return Math.min(100, params.baseScore + bonus);
  }
};

// New helper function
const calculateHitQuality = (
  hit: HittingData,
  thresholds: LevelThresholds
): number => {
  // EV component (0-1)
  const evScore = Math.min(1, Math.max(0,
    (hit.ExitSpeed - thresholds.minEffectiveEV) /
    (thresholds.optimalEV - thresholds.minEffectiveEV)
  ));

  // Angle component
  const isLineDrive = hit.Angle >= thresholds.lineDriveAngles[0] &&
                     hit.Angle <= thresholds.lineDriveAngles[1];

  return evScore * (isLineDrive ? 1 : 0.7);
};

const analyzeMisses = (
  hits: HittingData[],
  thresholds: LevelThresholds
): { missScore: number, spreadBonus: number } => {
  if (hits.length < 4) return { missScore: 0, spreadBonus: 0 };

  // 1. Calculate hit qualities
  const qualities = hits.map(hit =>
    calculateHitQuality(hit, thresholds)
  ).sort((a, b) => a - b);

  // 2. Get worst 25%
  const worstCount = Math.ceil(hits.length * 0.25);
  const worstQualities = qualities.slice(0, worstCount);
  const avgMissQuality = Math.max(0.2, // Floor at 20%
    worstQualities.reduce((a, b) => a + b, 0) / worstCount
  );

  // 3. Calculate spread
  const positionsY = hits.map(h => h.ContactPositionY!);
  const spreadInches = (Math.max(...positionsY) - Math.min(...positionsY)) * 12;

  // 4. Spread bonus
  const spreadBonus = avgMissQuality >= thresholds.minMissQualityForSpread
    ? Math.min(20, (spreadInches / thresholds.maxSpreadInches) * 20)
    : 0;

  return {
    missScore: avgMissQuality * 80,
    spreadBonus
  };
};

const calculateSpinRateScore = (spinRate: number, exitVelocity: number): number => {
  if (!spinRate) return 0;
  const BelowOptimal_StartSR = 900, BelowOptimal_EndSR = 1199, Optimal_StartSR = 1200, Optimal_EndSR = 2500,
        AboveOptimal_StartSR = 2501, AboveOptimal_EndSR = 2999, Excessive_StartSR = 3000, Excessive_EndSR = 5000;
  const BelowOptimal_StartScore = 40, BelowOptimal_EndScore = 80, Optimal_StartScore = 80, Optimal_EndScore = 100,
        AboveOptimal_StartScore = 100, AboveOptimal_EndScore = 60, Excessive_StartScore = 60, Excessive_EndScore = 40;

  if (spinRate < BelowOptimal_StartSR) return BelowOptimal_StartScore;
  else if (spinRate <= BelowOptimal_EndSR)
    return ((spinRate - BelowOptimal_StartSR) / (BelowOptimal_EndSR - BelowOptimal_StartSR)) *
           (BelowOptimal_EndScore - BelowOptimal_StartScore) + BelowOptimal_StartScore;
  else if (spinRate <= Optimal_EndSR)
    return ((spinRate - Optimal_StartSR) / (Optimal_EndSR - Optimal_StartSR)) *
           (Optimal_EndScore - Optimal_StartScore) + Optimal_StartScore;
  else if (spinRate <= AboveOptimal_EndSR) {
    if (exitVelocity > 90) return Optimal_EndScore;
    return ((AboveOptimal_EndSR - spinRate) / (AboveOptimal_EndSR - AboveOptimal_StartSR)) *
           (AboveOptimal_StartScore - AboveOptimal_EndScore) + AboveOptimal_EndScore;
  } else if (spinRate <= Excessive_EndSR)
    return ((Excessive_EndSR - spinRate) / (Excessive_EndSR - Excessive_StartSR)) *
           (Excessive_StartScore - Excessive_EndScore) + Excessive_EndScore;
  return Excessive_EndScore;
};

const calculateSpinAxisScore = (spinAxis: number, exitVelocity: number): number => {
  if (!spinAxis) return 0;
  const Optimal_Min_SA = 120, Optimal_Max_SA = 240, Min_SA_Limit = 0, Max_SA_Limit = 360,
        Min_SA_Score = 0, Max_SA_Score = 100;
  if (spinAxis >= Optimal_Min_SA && spinAxis <= Optimal_Max_SA) return Max_SA_Score;
  if (exitVelocity > 90) {
    if (spinAxis < Optimal_Min_SA)
      return Max_SA_Score - ((Optimal_Min_SA - spinAxis) / (Optimal_Min_SA - Min_SA_Limit)) * (Max_SA_Score - Min_SA_Score);
    else
      return Max_SA_Score - ((spinAxis - Optimal_Max_SA) / (Max_SA_Limit - Optimal_Max_SA)) * (Max_SA_Score - Min_SA_Score);
  }
  return Min_SA_Score;
};

export const calculateQualityOfContactScore = (
  hits: HittingData[],
  benchmarks: LevelBenchmarks,
  effectiveLevel: 'Professional' | 'College' | 'High School',
  params: QualityOfContactParams = DEFAULT_PARAMS.qualityOfContact
): number => {
  if (!hits.length) return 0;
  console.log('Input hits:', hits);
  const hitScores = hits.map(hit => {
    const evScore = calculateEVScore(hit.ExitSpeed, benchmarks, effectiveLevel);
    const srScore = calculateSpinRateScore(hit.HitSpinRate, hit.ExitSpeed);
    const saScore = calculateSpinAxisScore(hit.HitSpinAxis, hit.ExitSpeed);
    console.log('Individual hit scores:', { evScore, srScore, saScore, exitVelo: hit.ExitSpeed });
    return (evScore * params.weight_ev) + (srScore * params.weight_sr) + (saScore * params.weight_sa);
  });
  const finalScore = hitScores.reduce((sum, score) => sum + score, 0) / hitScores.length;
  console.log('Final score:', finalScore);
  return finalScore;
};

const calculateConsistencyScore = (evStandardDeviation: number, validHits: number, params: RoomForErrorParams): number => {
  if (validHits <= 1) return 0;
  const maxPossibleStdDevEV = params.max_possible_stddev_ev || 20;
  const consistencyScalingConstant = params.consistency_scaling_constant || 0.2876820725;
  return Math.max(params.max_consistency_score * (1 - (evStandardDeviation / maxPossibleStdDevEV) * consistencyScalingConstant), 0);
};

const calculateLineDriveScore = (hits: HittingData[], params: RoomForErrorParams): number => {
  if (hits.length === 0) return 0;
  const lineDrives = hits.filter(hit =>
    hit.Angle >= params.line_drive_min_angle &&
    hit.Angle <= params.line_drive_max_angle &&
    hit.ExitSpeed >= params.min_ev_for_line_drive
  );
  return Math.min((lineDrives.length / hits.length) * params.max_line_drive_score, params.max_line_drive_score);
};

export const calculateRoomForErrorScore = (
  hits: HittingData[],
  validHits: number,
  params: RoomForErrorParams,
  effectiveLevel: 'Professional' | 'College' | 'High School'
): number => {
  if (validHits <= 1) return 0;
  console.log('Room for Error Input:', { hits, validHits, effectiveLevel });
  const levelScalingFactors = { Professional: 0.2, College: 1.0, "High School": 1.5 };
  const scalingFactor = levelScalingFactors[effectiveLevel];
  const contactPositions = hits.filter(hit => hit.ContactPositionY !== undefined).map(hit => hit.ContactPositionY!);
  const posYRange = contactPositions.length > 0 ? Math.max(...contactPositions) - Math.min(...contactPositions) : 0;
  const validExitSpeeds = hits.filter(hit => hit.ExitSpeed).map(hit => hit.ExitSpeed);
  const avgEV = validExitSpeeds.length > 0 ? validExitSpeeds.reduce((sum, ev) => sum + ev, 0) / validExitSpeeds.length : 0;
  const evStandardDeviation = validExitSpeeds.length > 0
    ? Math.sqrt(validExitSpeeds.reduce((sum, ev) => sum + Math.pow(ev - avgEV, 2), 0) / validExitSpeeds.length)
    : 0;
  const spreadScore = calculateSpreadScore(posYRange * scalingFactor, validExitSpeeds.length, params);
  const consistencyScore = calculateConsistencyScore(evStandardDeviation * scalingFactor, validExitSpeeds.length, params);
  const lineDriveScore = calculateLineDriveScore(hits, params);
  console.log('Spread Score:', spreadScore);
  console.log('Consistency Score:', consistencyScore);
  console.log('Line Drive Score:', lineDriveScore);
  const baseScore = spreadScore + consistencyScore + lineDriveScore;
  const decayRate = { Professional: 0.02, College: 0.015, "High School": 0.01 };
  return 100 * Math.exp(-decayRate[effectiveLevel] * (100 - baseScore));
};

const calculateSpreadScore = (range: number, validHits: number, params: RoomForErrorParams): number => {
  if (validHits === 0) return 0;
  const spreadScalingComponent = params.spread_scaling_constant || -Math.log(15 / params.max_spread_score);
  return params.max_spread_score * (1 - Math.exp(-spreadScalingComponent * range));
};

const calculateLaunchAngleProxScore = (
  angle: number,
  params: LaunchAngleParams = DEFAULT_PARAMS.launchAngle
): number => {
  // Immediately return 0 for negative angles.
  if (angle < 0) return 0;

  const idealMin = 5, idealMax = 20;
  if (angle >= idealMin && angle <= idealMax) return 100;

  const decayRate = 0.15; // Your adjusted decay rate
  if (angle < idealMin) {
    const deviation = idealMin - angle;
    return Math.max(params.min_la_score, 100 * Math.exp(-decayRate * deviation));
  } else {
    const deviation = angle - idealMax;
    return Math.max(params.min_la_score, 100 * Math.exp(-decayRate * deviation));
  }
};


const calculateEVProxScore = (
  exitVelocity: number,
  effectiveLevel: 'Professional' | 'College' | 'High School',
  params: LaunchAngleParams = DEFAULT_PARAMS.launchAngle
): number => {
  if (!exitVelocity) return 0;
  const benchmarks = LEVEL_BENCHMARKS[effectiveLevel];
  if (!benchmarks) return 0;
  const EV_Min_Value = benchmarks.minOptimalEV;
  const EV_Max_Value = benchmarks.maxOptimalEV;
  const EV_Scaling = params.ev_weight * 50;
  const Min_LA_Score = params.min_la_score;
  if (exitVelocity >= EV_Min_Value && exitVelocity <= EV_Max_Value) return 100;
  if (exitVelocity < EV_Min_Value) return Math.max(Min_LA_Score, 100 - ((EV_Min_Value - exitVelocity) / EV_Scaling) * 100);
  return Math.max(Min_LA_Score, 100 - ((exitVelocity - EV_Max_Value) / EV_Scaling) * 100);
};

const calculateLineDriveBonus = (
  angle: number,
  exitVelocity: number,
  effectiveLevel: 'Professional' | 'College' | 'High School',
  params: LaunchAngleParams = DEFAULT_PARAMS.launchAngle
): number => {
  const lineDriveMinLA = 5, lineDriveMaxLA = 20, highEVBonusMinLA = 20, highEVBonusMaxLA = 35, highEVThreshold = 95;
  const benchmarks = LEVEL_BENCHMARKS[effectiveLevel] || LEVEL_BENCHMARKS['College'];
  if (
    (angle >= lineDriveMinLA && angle <= lineDriveMaxLA && exitVelocity >= benchmarks.minOptimalEV) ||
    (angle >= highEVBonusMinLA && angle <= highEVBonusMaxLA && exitVelocity > highEVThreshold)
  ) {
    return params.line_drive_bonus;
  }
  return 0;
};

export const calculateLaunchAngleScore = (
  hit: HittingData,
  params: LaunchAngleParams = DEFAULT_PARAMS.launchAngle,
  effectiveLevel: 'Professional' | 'College' | 'High School'
): number => {
  if (!hit.Angle || !hit.ExitSpeed) return 0;
  const levelScalingFactors = { Professional: 0.8, College: 1.0, "High School": 1.2 };
  const scalingFactor = levelScalingFactors[effectiveLevel];
  const laProxScore = calculateLaunchAngleProxScore(hit.Angle, params);
  const evProxScore = calculateEVProxScore(hit.ExitSpeed, effectiveLevel, params);
  const combinedScore = ((laProxScore * params.la_weight + evProxScore * params.ev_weight) * scalingFactor);
  const hardHitBonus = (
    (hit.Angle >= 8 && hit.Angle <= 32 && hit.ExitSpeed >= 95) ? 20 :
    (hit.Angle >= 5 && hit.Angle <= 35 && hit.ExitSpeed >= 90) ? 15 :
    (hit.Angle >= 5 && hit.Angle <= 25 && hit.ExitSpeed >= getLevelMinEV(effectiveLevel)) ? 10 : 0
  );
  return Math.min(100, combinedScore + hardHitBonus);
};

const getLevelMinEV = (effectiveLevel: 'Professional' | 'College' | 'High School'): number => {
  const benchmarks = LEVEL_BENCHMARKS[effectiveLevel];
  return benchmarks ? benchmarks.minOptimalEV : LEVEL_BENCHMARKS.College.minOptimalEV;
};

export const calculateTotalHitScore = (
  hits: HittingData[],
  validHits: number,
  allHits: HittingData[],
  effectiveLevel: 'Professional' | 'College' | 'High School'
): number => {
  if (!hits.length || validHits === 0) return 0;
  const benchmarks = LEVEL_BENCHMARKS[effectiveLevel];
  if (!benchmarks) return 0;
  const qualityOfContactScore = calculateQualityOfContactScore(hits, benchmarks, effectiveLevel);
  const roomForErrorScore = calculateRoomForErrorScore(allHits, validHits, DEFAULT_PARAMS.roomForError, effectiveLevel);
  const avgExitSpeed = hits.reduce((sum, hit) => sum + hit.ExitSpeed, 0) / hits.length;
  const avgAngle = hits.reduce((sum, hit) => sum + hit.Angle, 0) / hits.length;
  const avgSpinRate = hits.reduce((sum, hit) => sum + (hit.HitSpinRate || 0), 0) / hits.length;
  const aggregatedHit: HittingData = { ...hits[0], ExitSpeed: avgExitSpeed, Angle: avgAngle, HitSpinRate: avgSpinRate, Level: effectiveLevel };
  const launchAngleScore = calculateLaunchAngleScore(aggregatedHit, DEFAULT_PARAMS.launchAngle, effectiveLevel);
  return Math.min(100, (qualityOfContactScore * 0.4) + (roomForErrorScore * 0.3) + (launchAngleScore * 0.3));
};

const getHitColor = (exitVelo: number, launchAngle: number): string => {
  if (launchAngle < 10) return '#ff6b6b'; // Softer red for ground balls
  if (launchAngle <= 25) return '#e55a2b'; // Softer burnt orange for line drives
  return '#ff8c5a'; // Lighter orange for fly balls
};

const calculateYPosition = (value: number, minDomain: number, maxDomain: number, height: number) => {
  const domainRange = maxDomain - minDomain;
  const percentage = (maxDomain - value) / domainRange;
  return 40 + (percentage * height);
};

const calculateZoneHeight = (start: number, end: number, minDomain: number, maxDomain: number, height: number) => {
  const startY = calculateYPosition(start, minDomain, maxDomain, height);
  const endY = calculateYPosition(end, minDomain, maxDomain, height);
  return Math.abs(endY - startY);
};

//
// ─── MAIN COMPONENT ───────────────────────────────────────────────────────────────
//
export default function HittingMetricsDashboard(): React.ReactElement {
  console.log('Component rendering');

  const [data, setData] = useState<HittingData[]>([]);
  const [selectedBatter, setSelectedBatter] = useState<string>('');
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [viewMode, setViewMode] = useState<'player' | 'coach'>('player');
  const [isPdfBuilderOpen, setIsPdfBuilderOpen] = useState(false);
  const [showEventTimeline, setShowEventTimeline] = useState(false);
  const [selectedEvent, setSelectedEvent] = useState<string | null>(null);
  const [selectedLevel, setSelectedLevel] = useState<'Professional' | 'College' | 'High School'>('College');
  const [uploadError, setUploadError] = useState<string>('');
  const [isLoading, setIsLoading] = useState(false);
  const [showTrendAnalysis, setShowTrendAnalysis] = useState(false);
  const [selectedTimeRange, setSelectedTimeRange] = useState<string>("30");
  const [selectedExitVelocityRanges, setSelectedExitVelocityRanges] = useState<VelocityRange[]>([]);
  const [selectedPitchVelocityRanges, setSelectedPitchVelocityRanges] = useState<VelocityRange[]>([]);

  const selectedPlayerHits = useMemo(() => data.filter(hit => hit.Batter === selectedBatter), [data, selectedBatter]);

  const calculatedMetrics = useMemo(() => {
    if (!selectedPlayerHits.length) return {
      'Quality of Contact': 0,
      'Launch Angle': 0,
      'Room for Error': 0,
      'Total Score': 0
    };

    // For launch angle, we use an aggregated hit:
    const avgExitSpeed = selectedPlayerHits.reduce((sum, hit) => sum + hit.ExitSpeed, 0) / selectedPlayerHits.length;
    const avgAngle = selectedPlayerHits.reduce((sum, hit) => sum + hit.Angle, 0) / selectedPlayerHits.length;
    const avgSpinRate = selectedPlayerHits.reduce((sum, hit) => sum + (hit.HitSpinRate || 0), 0) / selectedPlayerHits.length;
    const aggregatedHit: HittingData = {
      ...selectedPlayerHits[0],
      ExitSpeed: avgExitSpeed,
      Angle: avgAngle,
      HitSpinRate: avgSpinRate,
      Level: selectedLevel
    };

    const quality = calculateQualityOfContactScore(selectedPlayerHits, LEVEL_BENCHMARKS[selectedLevel], selectedLevel);
    const roomForError = calculateRoomForErrorScore(selectedPlayerHits, selectedPlayerHits.length, DEFAULT_PARAMS.roomForError, selectedLevel);
    const launchAngle = calculateLaunchAngleScore(aggregatedHit, DEFAULT_PARAMS.launchAngle, selectedLevel);
    const total = calculateTotalHitScore(selectedPlayerHits, selectedPlayerHits.length, selectedPlayerHits, selectedLevel);

    return {
      'Quality of Contact': quality,
      'Launch Angle': launchAngle,
      'Room for Error': roomForError,
      'Total Score': total
    };
  }, [selectedPlayerHits, selectedLevel]);

  const router = useRouter();
  const fileInputRef = useRef<HTMLInputElement>(null);
  const filteredData = useFilteredData(data, selectedExitVelocityRanges, selectedPitchVelocityRanges);

  const playerData = useMemo(() => selectedBatter ? data.filter(hit => hit.Batter === selectedBatter) : [], [data, selectedBatter]);

  const domains = useMemo(() => {
    if (!playerData.length) return { x: [40, 110], y: [-20, 50] };
    const evMin = Math.floor(Math.min(...playerData.map(d => d.ExitSpeed)) - 5);
    const evMax = Math.ceil(Math.max(...playerData.map(d => d.ExitSpeed)) + 5);
    const laMin = Math.floor(Math.min(...playerData.map(d => d.Angle)) - 5);
    const laMax = Math.ceil(Math.max(...playerData.map(d => d.Angle)) + 5);
    return {
      x: [Math.max(40, evMin), Math.min(110, evMax)],
      y: [Math.max(-20, laMin), Math.min(50, laMax)]
    };
  }, [playerData]);

  useEffect(() => {
    console.log('Filtering Stats:', {
      totalData: data.length,
      filteredCount: filteredData.length,
      selectedExitRanges: selectedExitVelocityRanges.map(r => r.label),
      selectedPitchRanges: selectedPitchVelocityRanges.map(r => r.label),
      sampleFilteredData: filteredData.slice(0, 3).map(d => ({
        exitVelo: d.ExitSpeed,
        pitchVelo: d.RelSpeed
      }))
    });
  }, [data, filteredData, selectedExitVelocityRanges, selectedPitchVelocityRanges]);

  useEffect(() => {
    const urlParams = new URLSearchParams(window.location.search);
    const batterParam = urlParams.get('selectedBatter');
    const levelParam = urlParams.get('selectedLevel');
    if (batterParam) setSelectedBatter(decodeURIComponent(batterParam));
    if (levelParam) setSelectedLevel(levelParam as 'Professional' | 'College' | 'High School');
  }, []);

  const batters = useMemo(() => [...new Set(data.map(hit => hit.Batter))], [data]);

  const validData = useMemo(() => {
    if (!selectedBatter) return [];
    return data.filter(hit => hit.Batter === selectedBatter && hit.ExitSpeed && hit.Angle);
  }, [data, selectedBatter]);

  const chartDomains = useMemo(() => {
    if (!validData.length) return { x: [40, 110], y: [-20, 50] };
    const evMin = Math.floor(Math.min(...validData.map(d => d.ExitSpeed)) - 5);
    const evMax = Math.ceil(Math.max(...validData.map(d => d.ExitSpeed)) + 5);
    const laMin = Math.floor(Math.min(...validData.map(d => d.Angle)) - 5);
    const laMax = Math.ceil(Math.max(...validData.map(d => d.Angle)) + 5);
    return {
      x: [Math.max(40, evMin), Math.min(110, evMax)],
      y: [Math.max(-20, laMin), Math.min(50, laMax)]
    };
  }, [validData]);

  const handleUploadClick = () => {
    fileInputRef.current?.click();
  };

  const handleFileUpload = (event: ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;
    setIsLoading(true);
    setUploadError('');
    setData([]);
    setSelectedBatter('');
    if (!file.name.endsWith('.csv')) {
      setUploadError('Please upload a valid CSV file');
      setIsLoading(false);
      return;
    }
    const reader = new FileReader();
    reader.onload = (e) => {
      try {
        const text = e.target?.result as string;
        const rows = text.split('\n');
        const headers = rows[0].split(',');
        const parsedData = rows.slice(1)
          .filter(row => row.trim())
          .map(row => {
            const values = row.split(',');
            const obj: Partial<HittingData> = {};
            headers.forEach((header, index) => {
              const key = header.trim() as keyof HittingData;
              const value = values[index]?.trim();
              if (key === 'Batter' || key === 'BatterSide' || key === 'Level' ||
                  key === 'EventID' || key === 'Event Date' || key === 'Event Location' ||
                  key === 'Event Type' || key === 'Session ID' || key === 'Next Event') {
                if (key === 'Level') {
                  if (value === 'Professional' || value === 'College' || value === 'High School') {
                    obj[key] = value;
                  }
                } else {
                  obj[key] = value || '';
                }
              } else {
                const num = Number(value);
                obj[key] = isNaN(num) ? 0 : num;
              }
            });
            return obj as HittingData;
          })
          .filter(row => row.Batter && row.Batter.trim());
        if (parsedData.length === 0) {
          setUploadError('No valid data found in CSV');
          setIsLoading(false);
          return;
        }
        setData(parsedData);
        if (fileInputRef.current) fileInputRef.current.value = '';
      } catch (error) {
        console.error('Error parsing CSV:', error);
        setUploadError('Error parsing CSV file');
      } finally {
        setIsLoading(false);
      }
    };
    reader.onerror = () => {
      setUploadError('Error reading file');
      setIsLoading(false);
    };
    reader.readAsText(file);
  };

  return (
    <div
      className="min-h-screen w-full"
      style={{
        background: THEME_COLORS.background,
        color: THEME_COLORS.text
      }}
    >
      <div className="w-full max-w-7xl mx-auto p-4 space-y-4">
        <Card className={CARD_VARIANTS.default}>
          <CardContent className="p-6">
            <div className="flex flex-col gap-4">
              <div className="flex items-center gap-4">
                <input
                  ref={fileInputRef}
                  type="file"
                  accept=".csv"
                  onChange={handleFileUpload}
                  className="hidden"
                />
                <Button
                  onClick={handleUploadClick}
                  variant="outline"
                  className={BUTTON_VARIANTS.upload}
                >
                  <Upload className="w-4 h-4" />
                  Upload Hitting Data
                </Button>
                {data.length > 0 && (
                  <span className="text-sm text-white">
                    {data.length} hits loaded
                  </span>
                )}
              </div>
              {uploadError && (
                <p className="text-sm text-red-400">
                  {uploadError}
                </p>
              )}
            </div>
          </CardContent>
        </Card>

        {data.length > 0 && (
          <>
            <div className="flex gap-4">
              <Select value={selectedBatter} onValueChange={setSelectedBatter}>
                <SelectTrigger className={SELECT_VARIANTS.trigger}>
                  <SelectValue placeholder="Select Batter" />
                </SelectTrigger>
                <SelectContent className={SELECT_VARIANTS.content}>
                  {batters.map(batter => (
                    <SelectItem key={batter} value={batter}>
                      {batter}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>

              <Select
                value={selectedLevel}
                onValueChange={(value: 'Professional' | 'College' | 'High School') => {
                  console.log('Level changed to:', value);
                  setSelectedLevel(value);
                }}
              >
                <SelectTrigger className={SELECT_VARIANTS.trigger}>
                  <SelectValue placeholder="Select Level" />
                </SelectTrigger>
                <SelectContent className={SELECT_VARIANTS.content}>
                  <SelectItem value="Professional">Professional</SelectItem>
                  <SelectItem value="College">College</SelectItem>
                  <SelectItem value="High School">High School</SelectItem>
                </SelectContent>
              </Select>
              <Button
                onClick={() => setViewMode(viewMode === 'player' ? 'coach' : 'player')}
                variant="outline"
                className={BUTTON_VARIANTS.outline}
              >
                {viewMode === 'player' ? 'Switch to Coach View' : 'Switch to Player View'}
              </Button>
              <Button
                onClick={() => setShowEventTimeline(!showEventTimeline)}
                variant="outline"
                className={BUTTON_VARIANTS.outlineHover}
              >
                {showEventTimeline ? 'Hide Event Timeline' : 'Show Event Timeline'}
              </Button>
              <Button onClick={() => setIsPdfBuilderOpen(true)}>Build Report</Button>
              <PdfBuilderModal
                isOpen={isPdfBuilderOpen}
                onClose={() => setIsPdfBuilderOpen(false)}
                players={batters}
                data={data}
                selectedLevel={selectedLevel}
                DEFAULT_PARAMS={DEFAULT_PARAMS}
                metrics={['Quality of Contact', 'Launch Angle', 'Room for Error', 'Total Score']}
                calculatedMetrics={calculatedMetrics}
              />
            </div>
            {viewMode === 'player' ? (
              selectedBatter ? (
                <>
                  <div className="mt-4">
                    <VelocityFilter
                      exitRanges={DEFAULT_VELOCITY_RANGES.exit}
                      pitchRanges={DEFAULT_VELOCITY_RANGES.pitch}
                      selectedExitRanges={selectedExitVelocityRanges}
                      selectedPitchRanges={selectedPitchVelocityRanges}
                      onExitRangeChange={setSelectedExitVelocityRanges}
                      onPitchRangeChange={setSelectedPitchVelocityRanges}
                    />
                  </div>
                  {showEventTimeline && (
                    <Card className={`${CARD_VARIANTS.floating} mt-6`}>
                      <CardHeader>
                        <CardTitle className="text-white">Event Timeline</CardTitle>
                      </CardHeader>
                      <CardContent className="pt-6 text-white">
                        <div className="relative py-8">
                          <div className="absolute left-0 top-1/2 w-full h-0.5 bg-white/30"></div>
                          <div className="relative flex justify-between px-4">
                            {data
                              .filter(hit => hit.Batter === selectedBatter)
                              .filter((hit, index, self) =>
                                index === self.findIndex((h) => h.EventID === hit.EventID && h.EventID)
                              )
                              .sort((a, b) => (a["Event Date"] || '').localeCompare(b["Event Date"] || ''))
                              .map((event, index) => (
                                <div key={index} className="flex flex-col items-center w-32">
                                  <div
                                    onClick={() => setSelectedEvent(event.EventID || null)}
                                    className={`w-5 h-5 rounded-full border-2 border-white shadow-md ${
                                      event["Event Type"]?.includes('Baseline')
                                        ? 'bg-[#e55a2b] hover:bg-[#ff8c5a]'
                                        : 'bg-[#ff8c5a] hover:bg-[#ffcc5a]'
                                    } ${
                                      event.EventID === selectedEvent ? 'ring-2 ring-offset-2 ring-[#e55a2b]' : ''
                                    } z-10 cursor-pointer transition-colors duration-200`}
                                  ></div>
                                  <div className="mt-2 text-sm font-medium text-center w-full">
                                    <div className="font-semibold truncate text-white">{event["Event Type"]}</div>
                                    <div className="text-xs text-gray-300">{event["Event Date"]}</div>
                                    <div className="text-xs text-gray-400">{event["Event Location"]}</div>
                                  </div>
                                </div>
                              ))}
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  )}
                  {selectedEvent && (
                    <Card className={`${CARD_VARIANTS.floating} mt-6`}>
                      <CardContent className="pt-6">
                        <div className="grid grid-cols-3 gap-4">
                          <div className="col-span-1">
                            <h3 className="font-semibold text-lg mb-2 text-white">
                              {data.find(hit => hit.EventID === selectedEvent)?.["Event Type"]}
                            </h3>
                            <div className="space-y-1">
                              <p className="text-sm text-gray-300">
                                <span className="font-medium">Date:</span> {data.find(hit => hit.EventID === selectedEvent)?.["Event Date"]}
                              </p>
                              <p className="text-sm text-gray-300">
                                <span className="font-medium">Location:</span> {data.find(hit => hit.EventID === selectedEvent)?.["Event Location"]}
                              </p>
                              <p className="text-sm text-gray-300">
                                <span className="font-medium">Session ID:</span> {data.find(hit => hit.EventID === selectedEvent)?.["Session ID"]}
                              </p>
                            </div>
                          </div>
                          <div className="col-span-2">
                            <div className="grid grid-cols-2 gap-6 max-w-md mx-auto">
                              <div className="flex flex-col items-center">
                                {(() => {
                                  console.log("Score Gauge Input:", {
                                    selectedLevel,
                                    benchmarks: LEVEL_BENCHMARKS[selectedLevel],
                                    hitData: data.find(hit => hit.Batter === selectedBatter)
                                  });
                                  return (
                                    <ScoreGauge
                                      score={calculateQualityOfContactScore(
                                        data.filter(hit => hit.EventID === selectedEvent),
                                        LEVEL_BENCHMARKS[selectedLevel],
                                        selectedLevel
                                      )}
                                      size="md"
                                      label="Quality of Contact"
                                    />
                                  );
                                })()}
                              </div>
                              <div className="flex flex-col items-center">
                                <ScoreGauge
                                  score={calculateRoomForErrorScore(
                                    data.filter(hit => hit.Batter === selectedBatter),
                                    data.filter(hit => hit.Batter === selectedBatter).length,
                                    DEFAULT_PARAMS.roomForError,
                                    selectedLevel
                                  )}
                                  size="md"
                                  label="Room for Error"
                                />
                              </div>
                              <div className="flex flex-col items-center">
                                <ScoreGauge
                                  score={calculateLaunchAngleScore(
                                    data.find(hit => hit.EventID === selectedEvent) || data[0],
                                    DEFAULT_PARAMS.launchAngle,
                                    selectedLevel
                                  )}
                                  size="md"
                                  label="Launch Angle"
                                />
                              </div>
                              <div className="flex flex-col items-center">
                                <ScoreGauge
                                  score={calculateTotalHitScore(
                                    data.filter(hit => hit.EventID === selectedEvent),
                                    data.filter(hit => hit.EventID === selectedEvent).length,
                                    data.filter(hit => hit.EventID === selectedEvent),
                                    selectedLevel
                                  )}
                                  size="md"
                                  label="Event Score"
                                />
                              </div>
                            </div>
                            <div className="flex justify-center mt-6">
                              <Button
                                onClick={() => setSelectedEvent(null)}
                                variant="outline"
                                className={BUTTON_VARIANTS.outline}
                              >
                                Close Details
                              </Button>
                            </div>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  )}
                  <Button
                    onClick={() => router.push(`/trend-analysis?batter=${encodeURIComponent(selectedBatter)}&level=${encodeURIComponent(selectedLevel)}`)}
                    variant="outline"
                    className={BUTTON_VARIANTS.outline}
                  >
                    View Trend Analysis
                  </Button>
                  {showTrendAnalysis && (
                    <div className="col-span-2">
                      <HittingTrendAnalysis
                        data={data}
                        selectedBatter={selectedBatter}
                        selectedLevel={selectedLevel}
                        timeRange={selectedTimeRange}
                      />
                    </div>
                  )}
                  <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
                    <Card className={CARD_VARIANTS.floating}>
                      <CardHeader>
                        <CardTitle className="text-white text-center">Quality of Contact</CardTitle>
                      </CardHeader>
                      <CardContent>
                        <div className="flex flex-col items-center space-y-4">
                          <ScoreGauge
                            score={calculateQualityOfContactScore(
                              data.filter(hit => hit.Batter === selectedBatter),
                              LEVEL_BENCHMARKS[selectedLevel],
                              selectedLevel
                            )}
                            size="lg"
                            label="Quality Score"
                          />
                          <div className="grid grid-cols-3 gap-4 w-full text-center">
                            <div>
                              <p className="text-sm font-medium text-gray-400">EV</p>
                              <p className="text-lg font-semibold text-white">
                                {(data.filter(hit => hit.Batter === selectedBatter && hit.ExitSpeed)
                                  .reduce((sum, hit) => sum + hit.ExitSpeed, 0) /
                                  data.filter(hit => hit.Batter === selectedBatter && hit.ExitSpeed).length
                                ).toFixed(1) || 0}
                                mph
                              </p>
                            </div>
                            <div>
                              <p className="text-sm font-medium text-gray-400">Spin Rate</p>
                              <p className="text-lg font-semibold text-white">
                                {Math.round(
                                  data.filter(hit => hit.Batter === selectedBatter && hit.HitSpinRate)
                                  .reduce((sum, hit) => sum + hit.HitSpinRate, 0) /
                                  data.filter(hit => hit.Batter === selectedBatter && hit.HitSpinRate).length
                                ) || 0}
                                rpm
                              </p>
                            </div>
                            <div>
                              <p className="text-sm font-medium text-gray-400">Spin Axis</p>
                              <p className="text-lg font-semibold text-white">
                                {Math.round(
                                  data.filter(hit => hit.Batter === selectedBatter && hit.HitSpinAxis)
                                  .reduce((sum, hit) => sum + hit.HitSpinAxis, 0) /
                                  data.filter(hit => hit.Batter === selectedBatter && hit.HitSpinAxis).length
                                ) || 0}°
                              </p>
                            </div>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                    <Card className={CARD_VARIANTS.floating}>
                      <CardHeader>
                        <CardTitle className="text-white text-center">Room for Error</CardTitle>
                      </CardHeader>
                      <CardContent>
                        <div className="flex flex-col items-center space-y-4">
                          <ScoreGauge
                            score={calculateRoomForErrorScore(
                              data.filter(hit => hit.Batter === selectedBatter),
                              data.filter(hit => hit.Batter === selectedBatter).length,
                              DEFAULT_PARAMS.roomForError,
                              selectedLevel
                            )}
                            size="lg"
                            label="Consistency Score"
                          />
                        </div>
                      </CardContent>
                    </Card>
                    <Card className={CARD_VARIANTS.floating}>
                      <CardHeader>
                        <CardTitle className="text-white text-center">Slugging Potential</CardTitle>
                      </CardHeader>
                      <CardContent>
                        <div className="flex flex-col items-center space-y-4">
                          <ScoreGauge
                            score={calculateLaunchAngleScore(
                              data.find(hit => hit.Batter === selectedBatter) || data[0],
                              DEFAULT_PARAMS.launchAngle,
                              selectedLevel
                            )}
                            size="lg"
                            label="Launch Score"
                          />
                          <div className="grid grid-cols-2 gap-4 w-full text-center">
                            <div>
                              <p className="text-sm font-medium text-gray-400">Avg Launch Angle</p>
                              <p className="text-lg text-white font-semibold">
                                {(data.filter(hit => hit.Batter === selectedBatter && hit.Angle)
                                  .reduce((sum, hit) => sum + hit.Angle, 0) /
                                  data.filter(hit => hit.Batter === selectedBatter && hit.Angle).length
                                ).toFixed(1) || 0}°
                              </p>
                            </div>
                            <div>
                              <p className="text-sm font-medium text-gray-400">Line Drive %</p>
                              <p className="text-lg text-white font-semibold">
                                {(data.filter(hit =>
                                  hit.Batter === selectedBatter &&
                                  hit.Angle >= 5 &&
                                  hit.Angle <= 20
                                ).length /
                                data.filter(hit => hit.Batter === selectedBatter).length * 100
                                ).toFixed(1)}%
                              </p>
                            </div>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                    <Card className={CARD_VARIANTS.floating}>
                      <CardHeader>
                        <CardTitle className="text-white text-center">Overall Hitting Score</CardTitle>
                      </CardHeader>
                      <CardContent>
                        <div className="flex flex-col items-center space-y-4">
                          <ScoreGauge
                            score={calculateTotalHitScore(
                              data.filter(hit => hit.Batter === selectedBatter),
                              data.filter(hit => hit.Batter === selectedBatter).length,
                              data.filter(hit => hit.Batter === selectedBatter),
                              selectedLevel
                            )}
                            size="lg"
                            label="Total Score"
                          />
                        </div>
                      </CardContent>
                    </Card>
                  </div>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mt-8">
                    <Card className={CARD_VARIANTS.floating}>
                      <CardHeader>
                        <CardTitle className="text-white">Contact Point Analysis</CardTitle>
                      </CardHeader>
                      <CardContent className="flex flex-col items-center">
                        <div className="w-full h-[400px] flex items-center justify-center">
                          <ContactHeatmap
                            data={data}
                            selectedBatter={selectedBatter}
                            selectedLevel={selectedLevel}
                          />
                        </div>
                      </CardContent>
                    </Card>
                    <Card className={CARD_VARIANTS.floating}>
                      <CardHeader>
                        <CardTitle className="text-white">Launch Angle vs Exit Velocity</CardTitle>
                      </CardHeader>
                      <CardContent>
                        {(() => {
                          const filteredHits = data.filter(hit => hit.Batter === selectedBatter);
                          const angles = filteredHits.map(hit => hit.Angle);
                          const minAngle = Math.floor(Math.min(...angles));
                          const maxAngle = Math.ceil(Math.max(...angles));
                          const yMin = Math.min(-12, minAngle - 5);
                          const yMax = Math.max(50, maxAngle + 5);
                          return (
                            <div className="h-[400px] w-full">
                              <ResponsiveContainer>
                                <ScatterChart margin={{ top: 40, right: 20, bottom: 20, left: 40 }}>
                                  <CartesianGrid strokeDasharray="3 3" stroke="white" opacity={0.2} />
                                  <rect
                                    x="100"
                                    y={calculateYPosition(10, yMin, yMax, 300)}
                                    width="90%"
                                    height={calculateZoneHeight(yMin, 10, yMin, yMax, 300)}
                                    fill="#ff6b6b"
                                    fillOpacity={0.1}
                                  />
                                  <rect
                                    x="100"
                                    y={calculateYPosition(25, yMin, yMax, 300)}
                                    width="90%"
                                    height={calculateZoneHeight(10, 25, yMin, yMax, 300)}
                                    fill="#e55a2b"
                                    fillOpacity={0.1}
                                  />
                                  <rect
                                    x="100"
                                    y={40}
                                    width="90%"
                                    height={calculateZoneHeight(25, yMax, yMin, yMax, 300)}
                                    fill="#ff8c5a"
                                    fillOpacity={0.1}
                                  />
                                  <g>
                                    <text x={60} y={20} fill="white" fontSize={12}>
                                      Ground Balls (&lt;10°)
                                    </text>
                                    <rect x={40} y={10} width={15} height={15} fill="#ff6b6b" fillOpacity={0.3} />
                                    <text x={220} y={20} fill="white" fontSize={12}>
                                      Line Drives (10-25°)
                                    </text>
                                    <rect x={200} y={10} width={15} height={15} fill="#e55a2b" fillOpacity={0.3} />
                                    <text x={380} y={20} fill="white" fontSize={12}>
                                      Fly Balls (&gt;25°)
                                    </text>
                                    <rect x={360} y={10} width={15} height={15} fill="#ff8c5a" fillOpacity={0.3} />
                                  </g>
                                  <XAxis
                                    type="number"
                                    dataKey="ExitSpeed"
                                    name="Exit Velocity"
                                    unit=" mph"
                                    domain={chartDomains.x}
                                    ticks={Array.from({ length: 6 }, (_, i) => Math.round(chartDomains.x[0] + (chartDomains.x[1] - chartDomains.x[0]) * i / 5))}
                                    label={{ value: 'Exit Velocity (mph)', position: 'bottom', offset: 5, fill: 'white' }}
                                    tick={{ fill: 'white' }}
                                  />
                                  <YAxis
                                    type="number"
                                    dataKey="Angle"
                                    name="Launch Angle"
                                    unit="°"
                                    domain={[yMin, yMax]}
                                    ticks={Array.from({ length: 8 }, (_, i) => yMin + (i * ((yMax - yMin) / 7)))}
                                    tickFormatter={(value) => Math.round(value).toString()}
                                    label={{ value: 'Launch Angle (°)', angle: -90, position: 'left', offset: 15, dy: -59, fill: 'white' }}
                                    tick={{ fill: 'white' }}
                                  />
                                  <Tooltip
                                    cursor={{ strokeDasharray: '3 3', stroke: 'white' }}
                                    content={({ payload }) => {
                                      if (payload && payload[0]) {
                                        const data = payload[0].payload;
                                        const hitType = data.Angle < 10 ? 'Ground Ball' : data.Angle <= 25 ? 'Line Drive' : 'Fly Ball';
                                        return (
                                          <div className="bg-gray-900 p-2 border border-[#e55a2b] rounded shadow-lg">
                                            <p className="font-semibold text-white">Exit Velo: {data.ExitSpeed.toFixed(1)} mph</p>
                                            <p className="font-semibold text-white">Launch Angle: {data.Angle.toFixed(1)}°</p>
                                            <p className="text-sm text-gray-300">{hitType}</p>
                                          </div>
                                        );
                                      }
                                      return null;
                                    }}
                                  />
                                  <Scatter
                                    data={data.filter(hit => hit.Batter === selectedBatter)}
                                    shape={(props: any) => (
                                      <circle
                                        cx={props.cx}
                                        cy={props.cy}
                                        r={5}
                                        fill={getHitColor(props.payload.ExitSpeed, props.payload.Angle)}
                                        style={{ opacity: 0.8 }}
                                      />
                                    )}
                                  />
                                </ScatterChart>
                              </ResponsiveContainer>
                            </div>
                          );
                        })()}
                      </CardContent>
                    </Card>
                    <Card className={CARD_VARIANTS.floating}>
                      <CardHeader>
                        <CardTitle className="text-white">Spray Chart</CardTitle>
                      </CardHeader>
                      <CardContent>
                        <SprayChart
                          data={data.map(hit => ({ ...hit, 'Event Date': hit['Event Date'] || 'Unknown' }))}
                          selectedBatter={selectedBatter}
                        />
                      </CardContent>
                    </Card>
                  </div>
                </>
              ) : null
            ) : (
              <CoachDashboard
                data={data}
                selectedLevel={selectedLevel}
                onPlayerSelect={(player: string) => setSelectedBatter(player)}
                dataType="hitting"
              />
            )}
          </>
        )}
      </div>

      {/* AI Chat Widget */}
      {selectedBatter && (
        <ChatWidget
          playerId={selectedBatter}
          playerName={selectedBatter}
          dataType="hitting"
          level={selectedLevel}
        />
      )}
    </div>
  );
}
