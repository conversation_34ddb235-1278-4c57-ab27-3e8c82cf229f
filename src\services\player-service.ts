/**
 * Player service for Firebase/Firestore data management
 * Implements the data layer described in the handoff document
 */

import { 
  collection, 
  doc, 
  getDocs, 
  getDoc, 
  addDoc, 
  updateDoc, 
  deleteDoc, 
  query, 
  where, 
  orderBy,
  Timestamp 
} from 'firebase/firestore';
import { db } from '@/lib/firebase';

// TypeScript interfaces as described in handoff document
export interface PlayerStats {
  // Hitting metrics
  exitVeloMax?: number;
  batSpeed?: number;
  onPlaneEfficiency?: number;
  rotationalAcceleration?: number;
  avgExitVelocity?: number;
  avgLaunchAngle?: number;
  avgDistance?: number;
  qualityOfContactScore?: number;
  launchAngleScore?: number;
  roomForErrorScore?: number;
  totalHitScore?: number;
  
  // Pitching metrics
  fastballVelo?: number;
  arsenalScore?: number;
  avgVelocity?: number;
  avgSpinRate?: number;
  avgVertBreak?: number;
  avgHorzBreak?: number;
  totalPitches?: number;
}

export interface StatsOverTime {
  date: Date;
  stats: PlayerStats;
  sessionId?: string;
  eventType?: string;
  location?: string;
}

export interface Player {
  id?: string;
  name: string;
  team?: string;
  position?: string;
  level?: 'Professional' | 'College' | 'High School';
  stats?: PlayerStats;
  statsOverTime?: StatsOverTime[];
  createdAt?: Date;
  updatedAt?: Date;
}

// Collection names
const COLLECTIONS = {
  PLAYERS: 'players',
  HITTING_DATA: 'hitting_data',
  PITCHING_DATA: 'pitching_data',
  SESSIONS: 'sessions'
} as const;

/**
 * Player CRUD operations
 */
export class PlayerService {
  
  /**
   * Get all players
   */
  static async getAllPlayers(): Promise<Player[]> {
    try {
      const playersRef = collection(db, COLLECTIONS.PLAYERS);
      const snapshot = await getDocs(query(playersRef, orderBy('name')));
      
      return snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data(),
        createdAt: doc.data().createdAt?.toDate(),
        updatedAt: doc.data().updatedAt?.toDate(),
        statsOverTime: doc.data().statsOverTime?.map((stat: any) => ({
          ...stat,
          date: stat.date?.toDate()
        }))
      })) as Player[];
    } catch (error) {
      console.error('Error fetching players:', error);
      throw error;
    }
  }

  /**
   * Get player by ID
   */
  static async getPlayerById(id: string): Promise<Player | null> {
    try {
      const playerRef = doc(db, COLLECTIONS.PLAYERS, id);
      const snapshot = await getDoc(playerRef);
      
      if (!snapshot.exists()) {
        return null;
      }

      const data = snapshot.data();
      return {
        id: snapshot.id,
        ...data,
        createdAt: data.createdAt?.toDate(),
        updatedAt: data.updatedAt?.toDate(),
        statsOverTime: data.statsOverTime?.map((stat: any) => ({
          ...stat,
          date: stat.date?.toDate()
        }))
      } as Player;
    } catch (error) {
      console.error('Error fetching player:', error);
      throw error;
    }
  }

  /**
   * Create new player
   */
  static async createPlayer(player: Omit<Player, 'id' | 'createdAt' | 'updatedAt'>): Promise<string> {
    try {
      const playersRef = collection(db, COLLECTIONS.PLAYERS);
      const now = Timestamp.now();
      
      const docRef = await addDoc(playersRef, {
        ...player,
        createdAt: now,
        updatedAt: now,
        statsOverTime: player.statsOverTime?.map(stat => ({
          ...stat,
          date: Timestamp.fromDate(stat.date)
        })) || []
      });
      
      return docRef.id;
    } catch (error) {
      console.error('Error creating player:', error);
      throw error;
    }
  }

  /**
   * Update player
   */
  static async updatePlayer(id: string, updates: Partial<Player>): Promise<void> {
    try {
      const playerRef = doc(db, COLLECTIONS.PLAYERS, id);
      const now = Timestamp.now();
      
      await updateDoc(playerRef, {
        ...updates,
        updatedAt: now,
        ...(updates.statsOverTime && {
          statsOverTime: updates.statsOverTime.map(stat => ({
            ...stat,
            date: Timestamp.fromDate(stat.date)
          }))
        })
      });
    } catch (error) {
      console.error('Error updating player:', error);
      throw error;
    }
  }

  /**
   * Delete player
   */
  static async deletePlayer(id: string): Promise<void> {
    try {
      const playerRef = doc(db, COLLECTIONS.PLAYERS, id);
      await deleteDoc(playerRef);
    } catch (error) {
      console.error('Error deleting player:', error);
      throw error;
    }
  }

  /**
   * Get players by team
   */
  static async getPlayersByTeam(team: string): Promise<Player[]> {
    try {
      const playersRef = collection(db, COLLECTIONS.PLAYERS);
      const q = query(playersRef, where('team', '==', team), orderBy('name'));
      const snapshot = await getDocs(q);
      
      return snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data(),
        createdAt: doc.data().createdAt?.toDate(),
        updatedAt: doc.data().updatedAt?.toDate(),
        statsOverTime: doc.data().statsOverTime?.map((stat: any) => ({
          ...stat,
          date: stat.date?.toDate()
        }))
      })) as Player[];
    } catch (error) {
      console.error('Error fetching players by team:', error);
      throw error;
    }
  }

  /**
   * Search players by name
   */
  static async searchPlayersByName(searchTerm: string): Promise<Player[]> {
    try {
      const playersRef = collection(db, COLLECTIONS.PLAYERS);
      const snapshot = await getDocs(playersRef);

      // Client-side filtering for name search (Firestore doesn't support full-text search)
      const players = snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data(),
        createdAt: doc.data().createdAt?.toDate(),
        updatedAt: doc.data().updatedAt?.toDate(),
        statsOverTime: doc.data().statsOverTime?.map((stat: any) => ({
          ...stat,
          date: stat.date?.toDate()
        }))
      })) as Player[];

      return players.filter(player =>
        player.name.toLowerCase().includes(searchTerm.toLowerCase())
      );
    } catch (error) {
      console.error('Error searching players:', error);
      throw error;
    }
  }

  /**
   * Migration utility: Import data from localStorage to Firebase
   */
  static async migrateFromLocalStorage(): Promise<void> {
    try {
      // Import localStorage utility
      const { getStorageData, STORAGE_KEYS } = await import('@/lib/storage');

      // Get data from localStorage
      const hittingData = getStorageData(STORAGE_KEYS.HITTING_DATA, []);
      const pitchingData = getStorageData(STORAGE_KEYS.PITCHING_DATA, []);

      // Extract unique players
      const hitters = new Set(hittingData.map((h: any) => h.Batter));
      const pitchers = new Set(pitchingData.map((p: any) => p.Pitcher));
      const allPlayers = new Set([...hitters, ...pitchers]);

      console.log(`Migrating ${allPlayers.size} players to Firebase...`);

      // Create player records
      for (const playerName of allPlayers) {
        const playerHits = hittingData.filter((h: any) => h.Batter === playerName);
        const playerPitches = pitchingData.filter((p: any) => p.Pitcher === playerName);

        // Calculate stats
        const stats: PlayerStats = {};

        if (playerHits.length > 0) {
          const avgExitVelocity = playerHits.reduce((sum: number, hit: any) => sum + hit.ExitSpeed, 0) / playerHits.length;
          const avgLaunchAngle = playerHits.reduce((sum: number, hit: any) => sum + hit.Angle, 0) / playerHits.length;
          const avgDistance = playerHits.reduce((sum: number, hit: any) => sum + (hit.Distance || 0), 0) / playerHits.length;

          stats.avgExitVelocity = Math.round(avgExitVelocity * 100) / 100;
          stats.avgLaunchAngle = Math.round(avgLaunchAngle * 100) / 100;
          stats.avgDistance = Math.round(avgDistance * 100) / 100;
          stats.qualityOfContactScore = Math.round((avgExitVelocity / 120) * 100);
          stats.totalHitScore = Math.round((stats.qualityOfContactScore + (avgLaunchAngle > 0 ? 80 : 60)) / 2);
        }

        if (playerPitches.length > 0) {
          const avgVelocity = playerPitches.reduce((sum: number, pitch: any) => sum + pitch.RelSpeed, 0) / playerPitches.length;
          const avgSpinRate = playerPitches.reduce((sum: number, pitch: any) => sum + (pitch.SpinRate || 0), 0) / playerPitches.length;

          stats.avgVelocity = Math.round(avgVelocity * 100) / 100;
          stats.avgSpinRate = Math.round(avgSpinRate);
          stats.arsenalScore = Math.round((avgVelocity / 100) * 100);
          stats.totalPitches = playerPitches.length;
        }

        // Create player record
        const player: Omit<Player, 'id' | 'createdAt' | 'updatedAt'> = {
          name: playerName,
          level: 'Professional', // Default level
          stats,
          statsOverTime: []
        };

        await this.createPlayer(player);
        console.log(`Migrated player: ${playerName}`);
      }

      console.log('Migration completed successfully!');
    } catch (error) {
      console.error('Error during migration:', error);
      throw error;
    }
  }

  /**
   * Fallback to localStorage if Firebase is unavailable
   */
  static async getPlayersWithFallback(): Promise<Player[]> {
    try {
      // Try Firebase first
      return await this.getAllPlayers();
    } catch (error) {
      console.warn('Firebase unavailable, falling back to localStorage:', error);

      // Fallback to localStorage
      const { getStorageData, STORAGE_KEYS } = await import('@/lib/storage');
      const hittingData = getStorageData(STORAGE_KEYS.HITTING_DATA, []);
      const pitchingData = getStorageData(STORAGE_KEYS.PITCHING_DATA, []);

      // Convert localStorage data to Player format
      const hitters = new Set(hittingData.map((h: any) => h.Batter));
      const pitchers = new Set(pitchingData.map((p: any) => p.Pitcher));
      const allPlayers = new Set([...hitters, ...pitchers]);

      return Array.from(allPlayers).map(playerName => ({
        id: playerName.replace(/\s+/g, '-').toLowerCase(),
        name: playerName,
        level: 'Professional' as const,
        stats: {},
        statsOverTime: [],
        createdAt: new Date(),
        updatedAt: new Date()
      }));
    }
  }
}
