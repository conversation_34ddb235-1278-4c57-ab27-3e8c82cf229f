// Simple test to verify the integration works
const testSimpleIntegration = async () => {
  try {
    console.log('Testing simple integration...');
    
    // Test with minimal data
    const testData = {
      playerId: 'test-player',
      question: 'Hello, can you help me?',
      dataType: 'hitting',
      level: 'Professional',
      hittingData: [],
      pitchingData: []
    };

    const response = await fetch('http://localhost:3000/api/query', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(testData),
    });

    console.log('Response status:', response.status);
    
    if (!response.ok) {
      const errorText = await response.text();
      console.log('Error response:', errorText);
      return;
    }

    const result = await response.json();
    console.log('✅ API is working!');
    console.log('Response:', result);
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
};

testSimpleIntegration();
