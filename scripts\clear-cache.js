#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

/**
 * <PERSON><PERSON>t to clear Next.js cache and help with development issues
 */

const projectRoot = path.join(__dirname, '..');
const nextDir = path.join(projectRoot, '.next');

function deleteFolderRecursive(folderPath) {
  if (fs.existsSync(folderPath)) {
    try {
      fs.readdirSync(folderPath).forEach((file) => {
        const curPath = path.join(folderPath, file);
        try {
          if (fs.lstatSync(curPath).isDirectory()) {
            deleteFolderRecursive(curPath);
          } else {
            // Handle Windows file permission issues
            try {
              fs.unlinkSync(curPath);
            } catch (err) {
              if (err.code === 'EPERM' || err.code === 'EBUSY') {
                // Try to change permissions and retry
                try {
                  fs.chmodSync(curPath, 0o666);
                  fs.unlinkSync(curPath);
                } catch (retryErr) {
                  console.warn(`⚠️  Could not delete file: ${curPath}`);
                }
              } else {
                throw err;
              }
            }
          }
        } catch (err) {
          console.warn(`⚠️  Could not process: ${curPath}`);
        }
      });

      try {
        fs.rmdirSync(folderPath);
      } catch (err) {
        if (err.code === 'ENOTEMPTY') {
          console.warn(`⚠️  Directory not empty, some files may be locked: ${folderPath}`);
        } else {
          throw err;
        }
      }
    } catch (err) {
      console.warn(`⚠️  Could not fully clear directory: ${folderPath}`);
    }
  }
}

function clearNextCache() {
  console.log('🧹 Clearing Next.js cache...');

  try {
    if (fs.existsSync(nextDir)) {
      deleteFolderRecursive(nextDir);

      // Verify if directory was successfully removed
      if (!fs.existsSync(nextDir)) {
        console.log('✅ Successfully cleared .next directory');
      } else {
        console.log('⚠️  .next directory partially cleared (some files may be locked)');
        console.log('💡 Try closing your IDE/editor and running the command again');
      }
    } else {
      console.log('ℹ️  .next directory does not exist');
    }
  } catch (error) {
    console.error('❌ Error clearing .next directory:', error.message);
    console.log('💡 Try closing your IDE/editor and running the command again');
  }
}

function showHelp() {
  console.log(`
🔧 BLP Cache Clearing Utility

This script helps resolve caching issues that require deleting .next folder.

Usage:
  node scripts/clear-cache.js

What this script does:
  - Deletes the .next directory (Next.js build cache)
  - This forces a complete rebuild on next start

After running this script:
  1. Run 'npm run dev' to start the development server
  2. The app will rebuild from scratch
  3. Any localStorage data will persist (use the debug panel in the app to clear that)

Common issues this fixes:
  - App only works after deleting .next folder
  - Stale build artifacts
  - Module resolution issues
  - Hot reload problems

Note: This only clears Next.js build cache, not browser localStorage.
Use the debug panel in the app (development mode) to clear localStorage.
`);
}

// Kill Next.js processes on Windows
function killNextProcesses() {
  return new Promise((resolve) => {
    console.log('🔪 Killing any running Next.js processes...');

    const { spawn } = require('child_process');

    // Kill node processes that might be running Next.js
    const killCommand = `
      Get-Process -Name "node" -ErrorAction SilentlyContinue | Where-Object {
        $_.CommandLine -like "*next*" -or $_.CommandLine -like "*dev*"
      } | Stop-Process -Force -ErrorAction SilentlyContinue

      # Also kill any processes using port 3000
      $port3000 = netstat -ano | findstr :3000
      if ($port3000) {
        $pids = $port3000 | ForEach-Object { ($_ -split '\s+')[-1] } | Sort-Object -Unique
        foreach ($pid in $pids) {
          if ($pid -match '^\d+$') {
            Stop-Process -Id $pid -Force -ErrorAction SilentlyContinue
          }
        }
      }

      Write-Host "Process cleanup completed."
    `;

    const ps = spawn('powershell', ['-Command', killCommand], {
      stdio: 'pipe',
      cwd: projectRoot
    });

    ps.on('close', () => {
      console.log('✅ Process cleanup completed');
      // Wait a moment for processes to fully terminate
      setTimeout(resolve, 1000);
    });

    ps.on('error', () => {
      console.log('⚠️  Process cleanup had issues, continuing...');
      resolve();
    });
  });
}

// Alternative method using PowerShell on Windows with aggressive clearing
async function clearNextCacheWindows() {
  console.log('🧹 Using Windows PowerShell to clear cache...');

  // First, kill any running processes
  await killNextProcesses();

  const { spawn } = require('child_process');

  const powershellCommand = `
    # Function to force delete with multiple attempts
    function Force-Delete {
      param($Path)

      if (Test-Path $Path) {
        Write-Host "Attempting to remove: $Path"

        # Method 1: Standard removal
        try {
          Remove-Item -Path $Path -Recurse -Force -ErrorAction Stop
          Write-Host "✅ Successfully removed with standard method"
          return $true
        } catch {
          Write-Host "⚠️  Standard method failed, trying aggressive methods..."
        }

        # Method 2: Take ownership and remove
        try {
          takeown /f "$Path" /r /d y 2>$null | Out-Null
          icacls "$Path" /grant administrators:F /t 2>$null | Out-Null
          Remove-Item -Path $Path -Recurse -Force -ErrorAction Stop
          Write-Host "✅ Successfully removed with ownership method"
          return $true
        } catch {
          Write-Host "⚠️  Ownership method failed, trying file-by-file..."
        }

        # Method 3: File by file removal
        try {
          Get-ChildItem -Path $Path -Recurse -Force | ForEach-Object {
            try {
              $_.Delete()
            } catch {
              # Ignore individual file errors
            }
          }
          Remove-Item -Path $Path -Force -ErrorAction SilentlyContinue
          Write-Host "✅ Completed file-by-file removal"
          return $true
        } catch {
          Write-Host "⚠️  Some files may still be locked"
        }
      }
      return $false
    }

    if (Test-Path ".next") {
      Write-Host "🗑️  Removing .next directory..."
      Force-Delete ".next"

      # Verify removal
      if (Test-Path ".next") {
        Write-Host "⚠️  Some files in .next may still be locked"
        Write-Host "💡 Try closing your IDE and running the command again"
      } else {
        Write-Host "✅ .next directory completely removed"
      }
    } else {
      Write-Host "ℹ️  .next directory does not exist"
    }

    Write-Host "Cache clearing completed."
  `;

  return new Promise((resolve) => {
    const ps = spawn('powershell', ['-Command', powershellCommand], {
      stdio: 'inherit',
      cwd: projectRoot
    });

    ps.on('close', (code) => {
      if (code === 0) {
        console.log('✅ Cache cleared successfully');
      } else {
        console.log('⚠️  Cache clearing completed with warnings');
      }
      resolve();
    });

    ps.on('error', (error) => {
      console.error('❌ PowerShell error:', error.message);
      console.log('Falling back to Node.js method...');
      clearNextCache();
      resolve();
    });
  });
}

// Main execution
async function main() {
  if (process.argv.includes('--help') || process.argv.includes('-h')) {
    showHelp();
    return;
  }

  console.log('🚀 BLP Cache Clearing Utility\n');

  // Try PowerShell on Windows, fallback to Node.js method
  if (process.platform === 'win32') {
    await clearNextCacheWindows();
  } else {
    clearNextCache();
  }

  console.log('\n💡 Now run "npm run dev" to start the development server');
  console.log('💡 Use the debug panel in the app to clear localStorage if needed');
}

// Run the main function
main().catch(console.error);
