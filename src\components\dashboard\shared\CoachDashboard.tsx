import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON>er, CardTitle, CardContent } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { ScoreGauge } from '../PitchingMetricsDashboard';
import SlideOverPanel from '@/components/ui/slide-over-panel';
import EventCarousel from './EventCarousel';
import { setStorageData, STORAGE_KEYS } from '@/lib/storage';
import { 
  calculateQualityOfContactScore,
  calculateRoomForErrorScore,
  calculateLaunchAngleScore,
  calculateTotalHitScore,
  LEVEL_BENCHMARKS
} from '@/components/utils/calculateScores';
import ContactHeatmap from '@/components/dashboard/Hitting/ContactHeatmap';
import SprayChart from '@/components/dashboard/Hitting/SprayChart';
import { Button } from '@/components/ui/button';
import { useRouter } from 'next/navigation';

// Define Level type
type Level = 'Professional' | 'College' | 'High School';

// HittingData interface matches the one from HittingMetricsDashboard
interface HittingData {
  Batter: string;
  BatterSide: string;
  ExitSpeed: number;
  Angle: number;
  Direction: number;
  HitSpinRate: number;
  ContactPositionY: number;
  HitSpinAxis: number;
  Level: Level;
  EventID?: string;
  "Event Date"?: string;
  "Event Location"?: string;
  "Event Type"?: string;
  "Session ID"?: string;
  "Next Event"?: string;
}

// PitchingData interface
interface PitchingData {
  Pitcher: string;
  AutoPitchType: string;
  RelSpeed: number;
  SpinRate: number;
  VertBreak: number;
  HorzBreak: number;
  Level: string;
}

// Props interface
interface CoachDashboardProps {
  data: any;
  selectedLevel: 'Professional' | 'College' | 'High School';
  onPlayerSelect: (player: string) => void;
  dataType: 'hitting' | 'pitching';
}

// Helper functions
const calculateHittingScore = (data: HittingData[], player: string): number => {
  const playerData = data.filter(d => d.Batter === player);
  if (playerData.length === 0) return 0;
  
  const avgExit = playerData.reduce((sum, d) => sum + d.ExitSpeed, 0) / playerData.length;
  return Math.min(100, Math.max(0, (avgExit / 100) * 100));
};

const calculatePitchingScore = (data: PitchingData[], player: string): number => {
  const playerData = data.filter(d => d.Pitcher === player);
  if (playerData.length === 0) return 0;
  
  const avgVelo = playerData.reduce((sum, d) => sum + d.RelSpeed, 0) / playerData.length;
  return Math.min(100, Math.max(0, (avgVelo / 95) * 100));
};

const CoachDashboard: React.FC<CoachDashboardProps> = ({
  data,
  selectedLevel,
  onPlayerSelect,
  dataType
}) => {
  const [sortOrder, setSortOrder] = useState<'highest' | 'lowest'>('highest');
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedPlayerDetails, setSelectedPlayerDetails] = useState<string | null>(null);
  const [viewType, setViewType] = useState<'grid' | 'list'>('grid');
  const [isDetailPanelOpen, setIsDetailPanelOpen] = useState(false);
  const [activeView, setActiveView] = useState<'heatmap' | 'spray'>('heatmap');

  const router = useRouter();

  const getPlayerEvents = (playerName: string) => {
    if (dataType === 'hitting') {
      const hittingData = data as HittingData[];
      return [...new Set(hittingData
        .filter(d => d.Batter === playerName)
        .map(d => ({
          id: d.EventID || '',
          date: d["Event Date"] || '',
          type: d["Event Type"] || 'Training Session',
          location: d["Event Location"],
        })))]
    }
    // Add similar logic for pitching data if needed
    return [];
  };

  // Get unique players and their scores
  const playerScores = React.useMemo<{
    player: string;
    score: number;
    qualityOfContactScore: number;
    roomForErrorScore: number;
    launchAngleScore: number;
  }[]>(() => {
    const players = new Set<string>();
    
    if (dataType === 'hitting') {
      const hittingData = data as HittingData[];
      hittingData.forEach(d => players.add(d.Batter));
      
      return Array.from(players).map(player => {
        const playerHits = hittingData.filter(d => d.Batter === player);
        
        if (playerHits.length === 0) return { player, score: 0, qualityOfContactScore: 0, roomForErrorScore: 0, launchAngleScore: 0 };
        
        // Get valid hits (those with both ExitSpeed and Angle)
        const validHits = playerHits.filter(hit => 
          hit.ExitSpeed && 
          typeof hit.Angle !== 'undefined'
        );
        
        if (validHits.length === 0) return { player, score: 0, qualityOfContactScore: 0, roomForErrorScore: 0, launchAngleScore: 0 };
        
        const qualityOfContactScore = calculateQualityOfContactScore(
          validHits[0],
          LEVEL_BENCHMARKS[selectedLevel]
        );
        
        const roomForErrorScore = calculateRoomForErrorScore(
          validHits,
          validHits.length
        );
        
        const launchAngleScore = calculateLaunchAngleScore(
          validHits[0]
        );
        
        // Apply weights exactly as in the spreadsheet formula
        const score = (qualityOfContactScore * 0.4) +  // 40%
                     (roomForErrorScore * 0.3) +       // 30%
                     (launchAngleScore * 0.3);         // 30%
        
        return {
          player,
          score: Math.round(score), // Round to nearest integer
          qualityOfContactScore: Math.round(qualityOfContactScore),
          roomForErrorScore: Math.round(roomForErrorScore),
          launchAngleScore: Math.round(launchAngleScore),
        };
      });
    } else {
      const pitchingData = data as PitchingData[];
      pitchingData.forEach(d => players.add(d.Pitcher));
      
      return Array.from(players).map(player => ({
        player,
        score: calculatePitchingScore(pitchingData, player),
        qualityOfContactScore: 0, // Default to 0 for pitching
        roomForErrorScore: 0, // Default to 0 for pitching
        launchAngleScore: 0, // Default to 0 for pitching
      }));
    }
  }, [data, dataType, selectedLevel]);
  

  // Sort players based on score
  const sortedPlayers = React.useMemo(() => {
    return [...playerScores].sort((a, b) => {
      return sortOrder === 'highest' 
        ? b.score - a.score 
        : a.score - b.score;
    });
  }, [playerScores, sortOrder]);

  const filteredPlayers = React.useMemo(() => {
    return sortedPlayers.filter((player) =>
      player.player.toLowerCase().includes(searchQuery.toLowerCase())
    );
  }, [sortedPlayers, searchQuery]) as {
    player: string;
    score: number;
    qualityOfContactScore: number;
    roomForErrorScore: number;
    launchAngleScore: number;
  }[];


  const calculateAvgExitVelo = (data: any[], selectedPlayer: string) => {
    const playerData = data.filter(hit => hit.Batter === selectedPlayer && hit.ExitSpeed);
    if (playerData.length === 0) return 0;
    return (playerData.reduce((sum, hit) => sum + hit.ExitSpeed, 0) / playerData.length).toFixed(1);
  };
  
  const calculateAvgLaunchAngle = (data: any[], selectedPlayer: string) => {
    const playerData = data.filter(hit => hit.Batter === selectedPlayer && hit.Angle);
    if (playerData.length === 0) return 0;
    return (playerData.reduce((sum, hit) => sum + hit.Angle, 0) / playerData.length).toFixed(1);
  };
  
  const calculateBarrels = (data: any[], selectedPlayer: string) => {
    return data.filter(hit => 
      hit.Batter === selectedPlayer && 
      hit.ExitSpeed >= 95 && 
      hit.Angle >= 10 && 
      hit.Angle <= 30
    ).length;
  };

    return (
            <div className="min-h-screen w-full style={{ backgroundColor: '#f6e1bd' }}">
              {/* Main content */}
              <div className="flex gap-2 text-black mb-4">
            <Button
                variant={viewType === 'grid' ? 'default' : 'outline'}
                onClick={() => setViewType('grid')}
                className="border border-black bg-white rounded-md px-4 py-2"
            >
                Grid View
            </Button>
            <Button
                variant={viewType === 'list' ? 'default' : 'outline'}
                onClick={() => setViewType('list')}
                className="border border-black bg-white rounded-md px-4 py-2"
            >
                List View
            </Button>
            </div>

            <div className="flex gap-4 items-center text-black mb-4">
                {/* Sort Dropdown */}
                <input
                    type="text"
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    placeholder="Search by player name"
                    className="border border-black rounded-md px-4 py-2 w-full"
                />

                <select
                    value={sortOrder}
                    onChange={(e) => setSortOrder(e.target.value as 'highest' | 'lowest')}
                    className="border border-black rounded-md px-4 py-2"
                >
                    <option value="highest">Sort by Highest Score</option>
                    <option value="lowest">Sort by Lowest Score</option>
                </select>
            </div>


            {viewType === 'grid' ? (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {filteredPlayers.map(({ player }) => (
              <div 
                  key={player} 
                  className="p-6 border rounded-lg bg-white border-black hover:bg-gray-50 cursor-pointer transition-all duration-200 hover:shadow-md"
                  onClick={() => {
                      setSelectedPlayerDetails(player);
                      setIsDetailPanelOpen(true);
                  }}
              >
                  <div className="flex flex-col items-center gap-4">
                      <h3 className="text-xl font-semibold text-black">{player}</h3>
                      <ScoreGauge
                          score={playerScores.find(p => p.player === player)?.score || 0}
                          size="lg"
                          label={dataType === 'pitching' ? 'Arsenal' : 'Total Hit Score'}
                      />
                  </div>
              </div>
          ))}
      </div>
  ) : (
<div className="space-y-4">
  {filteredPlayers.map(({ player }) => {
    const playerData = playerScores.find(p => p.player === player) || { score: 0, qualityOfContactScore: 0, roomForErrorScore: 0, launchAngleScore: 0 };
    return (
      <div 
        key={player} 
        className="p-4 border rounded-lg bg-white hover:bg-gray-50 cursor-pointer transition-all duration-200 hover:shadow-md"
        onClick={() => {
          setSelectedPlayerDetails(player);
          setIsDetailPanelOpen(true);
        }}
      >

        <div className="flex flex-col gap-4">
          <h3 className="text-lg font-semibold text-black">{player}</h3>
          <div className="flex flex-wrap gap-4">
            <div className="flex flex-col items-center">
              <ScoreGauge score={playerData.score || 0} size="sm" label="Total Hit Score" />
            </div>
            <div className="flex flex-col items-center">
              <ScoreGauge score={playerData.qualityOfContactScore || 0} size="sm" label="Quality of Contact" />
            </div>
            <div className="flex flex-col items-center">
              <ScoreGauge score={playerData.roomForErrorScore || 0} size="sm" label="Room for Error" />
            </div>
            <div className="flex flex-col items-center">
              <ScoreGauge score={playerData.launchAngleScore || 0} size="sm" label="Launch Angle" />
            </div>
          </div>
        </div>
      </div>
    );
  })}
</div>




  )}

  
      <SlideOverPanel 
        isOpen={isDetailPanelOpen}
        onClose={() => setIsDetailPanelOpen(false)}
        title={selectedPlayerDetails ? `${selectedPlayerDetails}'s Profile` : 'Player Profile'}
      >
        {selectedPlayerDetails && (
          <div className="h-full w-full">
            {/* Score Grid */}
            <div className="grid grid-cols-2 gap-4 p-6">
              <div className="bg-gray-50 p-4 rounded-lg">
                <h3 className="text-sm font-medium text-gray-500">Quality of Contact</h3>
                <ScoreGauge
                  score={
                    dataType === 'hitting'
                      ? calculateQualityOfContactScore(
                          { ...(data as HittingData[]).find(d => d.Batter === selectedPlayerDetails) || (data as HittingData[])[0], Level: selectedLevel },
                          LEVEL_BENCHMARKS[selectedLevel]
                        )
                      : 0
                  }
                  size="sm"
                  label="QoC Score"
                />
              </div>
              <div className="bg-gray-50 p-4 rounded-lg">
                <h3 className="text-sm font-medium text-gray-500">Launch Angle</h3>
                <ScoreGauge
                  score={
                    dataType === 'hitting'
                      ? calculateRoomForErrorScore(
                          (data as HittingData[]).filter(d => d.Batter === selectedPlayerDetails).map(hit => ({ ...hit, Level: selectedLevel })),
                          (data as HittingData[]).filter(d => d.Batter === selectedPlayerDetails).length
                        )
                      : 0
                  }
                  size="sm"
                  label="RFE Score"
                />
              </div>
              <div className="bg-gray-50 p-4 rounded-lg">
                <h3 className="text-sm font-medium text-gray-500">Room for Error</h3>
                <ScoreGauge
                  score={
                    dataType === 'hitting'
                      ? calculateRoomForErrorScore(
                          (data as HittingData[]).filter(d => d.Batter === selectedPlayerDetails),
                          (data as HittingData[]).filter(d => d.Batter === selectedPlayerDetails).length
                        )
                      : 0
                  }
                  size="sm"
                  label="RFE Score"
                />
              </div>
              <div className="bg-gray-50 p-4 rounded-lg">
                <h3 className="text-sm font-medium text-gray-500">Total Hit Score</h3>
                <ScoreGauge
                  score={
                    dataType === 'hitting'
                      ? calculateTotalHitScore(
                          { ...(data as HittingData[]).find(d => d.Batter === selectedPlayerDetails) || (data as HittingData[])[0], Level: selectedLevel },
                          (data as HittingData[]).filter(d => d.Batter === selectedPlayerDetails).length,
                          (data as HittingData[]).filter(d => d.Batter === selectedPlayerDetails).map(hit => ({ ...hit, Level: selectedLevel }))
                        )
                      : 0
                  }
                  size="sm"
                  label="Total Score"
                />
              </div>
            </div>
            <div className="flex justify-center mb-6 px-6">
                <Button
                  onClick={() => {
                    if (dataType === 'hitting') {
                      // Store the data using new storage utilities
                      setStorageData(STORAGE_KEYS.HITTING_DATA, data);
                      setStorageData(STORAGE_KEYS.SELECTED_BATTER, selectedPlayerDetails || '');
                      setStorageData(STORAGE_KEYS.SELECTED_LEVEL, selectedLevel);

                      // Close panel and navigate
                      setIsDetailPanelOpen(false);

                      // Call onPlayerSelect before navigation
                      onPlayerSelect(selectedPlayerDetails || '');

                      // Use window.location for a full page navigation
                      window.location.href = `/?view=hitting&selectedBatter=${encodeURIComponent(selectedPlayerDetails || '')}&selectedLevel=${selectedLevel}`;
                    }
                  }}
                  variant="outline"
                  className="w-full bg-white/80 text-black border-black/20 hover:bg-white/90"
                >
                  View Full Profile
                </Button>
              </div>

              {/* Contact Point Analysis */}
              <div className="w-full border-t">
                <div className="p-6 flex justify-between items-center">
                  <h3 className="text-lg font-semibold">
                    {activeView === 'heatmap' ? 'Contact Point Analysis' : 'Spray Chart'}
                  </h3>
                  <Button 
                    onClick={() => setActiveView(activeView === 'heatmap' ? 'spray' : 'heatmap')}
                    variant="outline"
                    className="bg-white/80 text-black border-black/20"
                  >
                    {activeView === 'heatmap' ? 'View Spray Chart' : 'View Heat Map'}
                  </Button>
                </div>
                <div className="w-full h-[600px]">
                  {activeView === 'heatmap' ? (
                    <ContactHeatmap 
                      data={data} 
                      selectedBatter={selectedPlayerDetails || ''} 
                      selectedLevel={selectedLevel}
                    />
                  ) : (
                    <SprayChart 
                      data={data}
                      selectedBatter={selectedPlayerDetails || ''}
                    />
                  )}
                </div>
              </div>
          </div>
        )}
      </SlideOverPanel>
    </div>
  );
};

export default CoachDashboard;